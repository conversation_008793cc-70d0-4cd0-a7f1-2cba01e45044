#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Edge-TTS连接和基本功能
"""

import subprocess
import sys
import time
from pathlib import Path

def test_edge_tts_basic():
    """测试Edge-TTS基本功能"""
    print("🧪 测试Edge-TTS基本功能...")
    
    # 简单的测试文本
    test_texts = [
        "你好",
        "测试",
        "这是一个简单的测试。",
        "大家好，欢迎观看视频。"
    ]
    
    # 基本语音
    test_voices = [
        "zh-CN-XiaoxiaoNeural",
        "zh-CN-YunxiNeural"
    ]
    
    success_count = 0
    total_tests = len(test_texts) * len(test_voices)
    
    for i, text in enumerate(test_texts):
        for j, voice in enumerate(test_voices):
            test_num = i * len(test_voices) + j + 1
            output_file = f"test_basic_{test_num}.wav"
            
            print(f"  测试 {test_num}/{total_tests}: {voice} - '{text}'")
            
            try:
                cmd = [
                    "edge-tts",
                    "--voice", voice,
                    "--text", text,
                    "--write-media", output_file
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0 and Path(output_file).exists():
                    file_size = Path(output_file).stat().st_size
                    if file_size > 500:
                        print(f"    ✅ 成功 ({file_size} bytes)")
                        success_count += 1
                        # 清理测试文件
                        Path(output_file).unlink()
                    else:
                        print(f"    ❌ 文件太小 ({file_size} bytes)")
                else:
                    print(f"    ❌ 失败: {result.stderr[:100] if result.stderr else '未知错误'}")
                
            except subprocess.TimeoutExpired:
                print(f"    ⏰ 超时")
            except Exception as e:
                print(f"    ❌ 异常: {e}")
            
            # 短暂延迟避免请求过快
            time.sleep(0.5)
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 成功")
    return success_count > 0

def test_network_connection():
    """测试网络连接"""
    print("🌐 测试网络连接...")
    
    try:
        import urllib.request
        import socket
        
        # 测试基本网络连接
        socket.setdefaulttimeout(10)
        
        # 测试DNS解析
        try:
            socket.gethostbyname('speech.platform.bing.com')
            print("  ✅ DNS解析正常")
        except:
            print("  ❌ DNS解析失败")
            return False
        
        # 测试HTTP连接
        try:
            response = urllib.request.urlopen('https://speech.platform.bing.com', timeout=10)
            print("  ✅ 网络连接正常")
            return True
        except Exception as e:
            print(f"  ❌ 网络连接失败: {e}")
            return False
            
    except Exception as e:
        print(f"  ❌ 网络测试异常: {e}")
        return False

def test_edge_tts_installation():
    """测试Edge-TTS安装"""
    print("📦 检查Edge-TTS安装...")
    
    try:
        result = subprocess.run(['edge-tts', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("  ✅ Edge-TTS已正确安装")
            return True
        else:
            print("  ❌ Edge-TTS安装有问题")
            return False
    except FileNotFoundError:
        print("  ❌ Edge-TTS未安装")
        return False
    except Exception as e:
        print(f"  ❌ 检查安装时出错: {e}")
        return False

def get_troubleshooting_tips():
    """获取故障排除建议"""
    tips = [
        "🔧 故障排除建议:",
        "",
        "1. 网络问题:",
        "   - 检查网络连接是否正常",
        "   - 尝试使用VPN或更换网络",
        "   - 检查防火墙设置",
        "",
        "2. Edge-TTS问题:",
        "   - 重新安装: pip uninstall edge-tts && pip install edge-tts",
        "   - 更新到最新版本: pip install --upgrade edge-tts",
        "   - 检查Python版本是否兼容",
        "",
        "3. 文本问题:",
        "   - 避免使用特殊字符",
        "   - 缩短文本长度",
        "   - 使用简单的中文文本",
        "",
        "4. 临时解决方案:",
        "   - 在程序中选择'pyttsx3 (本地)'语音引擎",
        "   - 或选择'设置无语音模式'",
        "",
        "5. 如果问题持续:",
        "   - 检查系统时间是否正确",
        "   - 重启计算机后重试",
        "   - 联系技术支持"
    ]
    
    return "\n".join(tips)

def main():
    print("Edge-TTS 连接和功能测试")
    print("=" * 40)
    
    # 测试安装
    if not test_edge_tts_installation():
        print("\n❌ Edge-TTS未正确安装，请先安装:")
        print("pip install edge-tts")
        return
    
    # 测试网络
    network_ok = test_network_connection()
    
    # 测试基本功能
    if network_ok:
        basic_ok = test_edge_tts_basic()
        
        if basic_ok:
            print("\n🎉 Edge-TTS工作正常！")
            print("✅ 可以在long_image_to_video.py中正常使用Edge-TTS")
        else:
            print("\n⚠️ Edge-TTS基本功能测试失败")
            print(get_troubleshooting_tips())
    else:
        print("\n❌ 网络连接问题，无法使用Edge-TTS")
        print(get_troubleshooting_tips())

if __name__ == "__main__":
    main()
