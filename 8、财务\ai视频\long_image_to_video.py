#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长图转视频工具 - 增强版
支持PDF和长图（JPG, PNG, BMP, TIFF等）转视频
支持文案生成、预览、编辑和本地文案选择
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import time
import json
import math

def check_and_install_fitz():
    """检查并安装PyMuPDF"""
    try:
        import fitz
        return fitz
    except ImportError:
        print("正在安装PyMuPDF...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PyMuPDF"])
            import fitz
            print("✅ PyMuPDF安装成功")
            return fitz
        except Exception as e:
            print(f"❌ PyMuPDF安装失败: {e}")
            return None

def check_dependencies():
    """检查依赖"""
    deps = {}
    
    # 检查PyMuPDF (只对PDF必需)
    fitz = check_and_install_fitz()
    deps['fitz'] = fitz
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        deps.update({'Image': Image, 'ImageDraw': ImageDraw, 'ImageFont': ImageFont})
    except ImportError:
        print("❌ 缺少Pillow，请运行: pip install Pillow")
        return None
    
    try:
        import pyttsx3
        deps['pyttsx3'] = pyttsx3
    except ImportError:
        print("⚠️ 缺少pyttsx3，将跳过本地语音功能")
        deps['pyttsx3'] = None
    
    # 检查Edge-TTS
    try:
        import subprocess
        result = subprocess.run(['pip', 'show', 'edge-tts'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("正在安装Edge-TTS...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "edge-tts"])
        deps['edge_tts'] = True
    except:
        deps['edge_tts'] = False
    
    try:
        from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips
        deps.update({
            'ImageClip': ImageClip,
            'AudioFileClip': AudioFileClip,
            'concatenate_videoclips': concatenate_videoclips
        })
    except ImportError:
        print("❌ MoviePy导入失败，请先运行: python fix_moviepy.py")
        return None
    
    return deps

class EnhancedImageVideoConverter:
    def __init__(self, deps):
        self.deps = deps
        self.temp_dir = Path(tempfile.mkdtemp())
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
        # 文案相关
        self.scripts_file = None
        self.use_local_scripts = False
        self.voice_engine = None
        self.selected_voice = None
        self.failed_voices = set()  # 记录失败的语音
        
        # 支持的图片格式
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
        self.supported_formats = {'.pdf'} | self.supported_image_formats
    
    def is_supported_file(self, file_path):
        """检查文件是否支持"""
        return Path(file_path).suffix.lower() in self.supported_formats
    
    def extract_content(self, file_path):
        """提取文件内容（PDF或图片）"""
        file_path = Path(file_path)
        suffix = file_path.suffix.lower()
        
        if suffix == '.pdf':
            return self.extract_pdf_content(file_path)
        elif suffix in self.supported_image_formats:
            return self.extract_image_content(file_path)
        else:
            print(f"❌ 不支持的文件格式: {suffix}")
            return []
    
    def extract_pdf_content(self, pdf_path):
        """提取PDF内容"""
        if not self.deps['fitz']:
            print("❌ PyMuPDF未安装，无法处理PDF文件")
            return []
        
        print("📖 正在提取PDF内容...")
        
        try:
            doc = self.deps['fitz'].open(str(pdf_path))
            pages_data = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # 提取文本
                text = page.get_text()
                
                # 转换为图片
                mat = self.deps['fitz'].Matrix(2.0, 2.0)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("ppm")
                
                from io import BytesIO
                img = self.deps['Image'].open(BytesIO(img_data))
                
                pages_data.append({
                    'page_num': page_num + 1,
                    'image': img,
                    'original_text': text.strip() if text.strip() else f"第{page_num + 1}页内容",
                    'script_text': '',
                    'use_audio': True
                })
                
                pix = None
            
            doc.close()
            print(f"✅ 成功提取 {len(pages_data)} 页内容")
            return pages_data
            
        except Exception as e:
            print(f"❌ PDF内容提取失败: {e}")
            return []
    
    def extract_image_content(self, image_path):
        """提取长图内容并分割"""
        print("🖼️ 正在处理长图...")
        
        try:
            # 打开图片
            original_img = self.deps['Image'].open(str(image_path))
            
            # 获取图片信息
            width, height = original_img.size
            print(f"📏 图片尺寸: {width} x {height}")
            
            # 判断是否为长图
            aspect_ratio = height / width
            print(f"📐 宽高比: {aspect_ratio:.2f}")
            
            if aspect_ratio <= 1.5:
                print("📱 检测到常规图片，将作为单页处理")
                return self.process_single_image(original_img, image_path.stem)
            else:
                print("📜 检测到长图，开始分割处理")
                return self.process_long_image(original_img, image_path.stem)
                
        except Exception as e:
            print(f"❌ 图片处理失败: {e}")
            return []
    
    def process_single_image(self, img, base_name):
        """处理单张图片"""
        pages_data = [{
            'page_num': 1,
            'image': img.copy(),
            'original_text': f"{base_name} - 完整内容",
            'script_text': '',
            'use_audio': True
        }]
        return pages_data
    
    def process_long_image(self, img, base_name):
        """分割长图"""
        width, height = img.size

        # 选择分割方式
        print("\n选择长图分割方式:")
        print("1. 自动分割（按屏幕比例）")
        print("2. 固定高度分割")
        print("3. 指定分割数量")
        print("4. 重叠滚动分割")
        print("5. 手动确定分割点（可视化）")

        choice = input("请选择 (1-5): ").strip()

        if choice == "2":
            return self.split_by_fixed_height(img, base_name)
        elif choice == "3":
            return self.split_by_count(img, base_name)
        elif choice == "4":
            return self.split_with_overlap(img, base_name)
        elif choice == "5":
            return self.split_manually(img, base_name)
        else:
            return self.split_by_aspect_ratio(img, base_name)
    
    def split_by_aspect_ratio(self, img, base_name):
        """按屏幕比例自动分割"""
        width, height = img.size
        target_ratio = 16 / 9  # 目标宽高比
        
        # 计算每段的高度
        segment_height = int(width / target_ratio)
        segments_count = math.ceil(height / segment_height)
        
        print(f"📊 将分割为 {segments_count} 段，每段高度约 {segment_height}px")
        
        pages_data = []
        for i in range(segments_count):
            y_start = i * segment_height
            y_end = min((i + 1) * segment_height, height)
            
            # 裁剪图片
            cropped = img.crop((0, y_start, width, y_end))
            
            # 如果最后一段太小，调整大小
            if cropped.height < segment_height * 0.5 and i > 0:
                # 合并到上一段
                pages_data[-1]['image'] = img.crop((0, (i-1) * segment_height, width, y_end))
                pages_data[-1]['original_text'] = f"{base_name} - 第{len(pages_data)}段（合并）"
            else:
                pages_data.append({
                    'page_num': len(pages_data) + 1,
                    'image': cropped,
                    'original_text': f"{base_name} - 第{len(pages_data) + 1}段",
                    'script_text': '',
                    'use_audio': True
                })
        
        print(f"✅ 成功分割为 {len(pages_data)} 段")
        return pages_data
    
    def split_by_fixed_height(self, img, base_name):
        """按固定高度分割"""
        width, height = img.size
        
        try:
            segment_height = int(input("请输入每段高度(像素): "))
        except ValueError:
            segment_height = 800  # 默认高度
        
        segments_count = math.ceil(height / segment_height)
        print(f"📊 将分割为 {segments_count} 段")
        
        pages_data = []
        for i in range(segments_count):
            y_start = i * segment_height
            y_end = min((i + 1) * segment_height, height)
            
            cropped = img.crop((0, y_start, width, y_end))
            pages_data.append({
                'page_num': i + 1,
                'image': cropped,
                'original_text': f"{base_name} - 第{i + 1}段",
                'script_text': '',
                'use_audio': True
            })
        
        print(f"✅ 成功分割为 {len(pages_data)} 段")
        return pages_data
    
    def split_by_count(self, img, base_name):
        """按指定数量分割"""
        width, height = img.size
        
        try:
            segments_count = int(input("请输入分割段数: "))
        except ValueError:
            segments_count = 5  # 默认5段
        
        segment_height = height // segments_count
        print(f"📊 将分割为 {segments_count} 段，每段高度约 {segment_height}px")
        
        pages_data = []
        for i in range(segments_count):
            y_start = i * segment_height
            y_end = (i + 1) * segment_height if i < segments_count - 1 else height
            
            cropped = img.crop((0, y_start, width, y_end))
            pages_data.append({
                'page_num': i + 1,
                'image': cropped,
                'original_text': f"{base_name} - 第{i + 1}段",
                'script_text': '',
                'use_audio': True
            })
        
        print(f"✅ 成功分割为 {len(pages_data)} 段")
        return pages_data
    
    def split_with_overlap(self, img, base_name):
        """重叠滚动分割"""
        width, height = img.size
        
        try:
            segment_height = int(input("请输入每段高度(像素): ") or "800")
            overlap_ratio = float(input("请输入重叠比例(0-0.5): ") or "0.2")
        except ValueError:
            segment_height = 800
            overlap_ratio = 0.2
        
        overlap_pixels = int(segment_height * overlap_ratio)
        step = segment_height - overlap_pixels
        
        segments_count = math.ceil((height - overlap_pixels) / step)
        print(f"📊 将分割为 {segments_count} 段，重叠 {overlap_pixels}px")
        
        pages_data = []
        for i in range(segments_count):
            y_start = i * step
            y_end = min(y_start + segment_height, height)
            
            cropped = img.crop((0, y_start, width, y_end))
            pages_data.append({
                'page_num': i + 1,
                'image': cropped,
                'original_text': f"{base_name} - 第{i + 1}段（滚动）",
                'script_text': '',
                'use_audio': True
            })
        
        print(f"✅ 成功分割为 {len(pages_data)} 段")
        return pages_data

    def split_manually(self, img, base_name):
        """手动确定分割点（可视化）"""
        width, height = img.size
        print(f"📏 图片尺寸: {width} x {height}")

        # 检查是否有tkinter支持
        try:
            import tkinter as tk
            from tkinter import messagebox, simpledialog
            from PIL import ImageTk
            return self.split_manually_gui(img, base_name)
        except ImportError:
            print("⚠️ 缺少tkinter，使用命令行模式")
            return self.split_manually_cli(img, base_name)

    def split_manually_gui(self, img, base_name):
        """使用GUI手动分割"""
        import tkinter as tk
        from tkinter import messagebox, simpledialog
        from PIL import ImageTk

        width, height = img.size
        split_points = []

        # 创建主窗口
        root = tk.Tk()
        root.title(f"手动分割 - {base_name}")
        root.geometry("1200x800")

        # 计算缩放比例以适应窗口
        max_display_height = 650
        scale_factor = min(1.0, max_display_height / height)
        display_width = int(width * scale_factor)
        display_height = int(height * scale_factor)

        # 缩放图片用于显示
        display_img = img.resize((display_width, display_height), self.deps['Image'].Resampling.LANCZOS)

        # 创建画布
        canvas_frame = tk.Frame(root)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        canvas = tk.Canvas(canvas_frame, width=display_width, height=display_height, bg='white')
        scrollbar_v = tk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollbar_h = tk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=canvas.xview)

        canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        canvas.configure(scrollregion=(0, 0, display_width, display_height))

        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 显示图片
        photo = ImageTk.PhotoImage(display_img)
        canvas.create_image(0, 0, anchor=tk.NW, image=photo)

        # 分割线数据结构：{line_id: {'y': actual_y, 'canvas_y': canvas_y, 'text_id': text_id}}
        split_lines = {}
        selected_line = None
        drag_start_y = None

        def on_click(event):
            """处理鼠标点击"""
            nonlocal selected_line, drag_start_y

            # 获取实际坐标（考虑滚动）
            canvas_y = canvas.canvasy(event.y)

            # 检查是否点击了现有的分割线
            clicked_line = None
            min_distance = float('inf')

            for line_id, line_data in split_lines.items():
                distance = abs(canvas_y - line_data['canvas_y'])
                if distance < 10 and distance < min_distance:  # 10像素的容差
                    clicked_line = line_id
                    min_distance = distance

            if clicked_line:
                # 选中分割线
                selected_line = clicked_line
                drag_start_y = canvas_y
                # 高亮选中的线
                canvas.itemconfig(clicked_line, fill='blue', width=3)
                # 取消其他线的高亮
                for line_id in split_lines:
                    if line_id != clicked_line:
                        canvas.itemconfig(line_id, fill='red', width=2)
            else:
                # 添加新的分割点
                add_split_point(canvas_y)

        def add_split_point(canvas_y):
            """添加分割点"""
            # 转换为原图坐标
            actual_y = int(canvas_y / scale_factor)

            if 0 < actual_y < height:
                # 检查是否太接近现有分割点
                for line_data in split_lines.values():
                    if abs(actual_y - line_data['y']) < 20:  # 最小间距20像素
                        return

                split_points.append(actual_y)
                split_points.sort()

                # 绘制分割线
                line_id = canvas.create_line(0, canvas_y, display_width, canvas_y,
                                           fill='red', width=2, tags='split_line')

                # 添加标签
                text_id = canvas.create_text(10, canvas_y - 15, text=f"Y:{actual_y}",
                                           fill='red', anchor=tk.NW, tags='split_text',
                                           font=('Arial', 10, 'bold'))

                # 存储分割线数据
                split_lines[line_id] = {
                    'y': actual_y,
                    'canvas_y': canvas_y,
                    'text_id': text_id
                }

                update_info()

        def on_drag(event):
            """处理鼠标拖拽"""
            nonlocal selected_line, drag_start_y

            if selected_line and drag_start_y is not None:
                canvas_y = canvas.canvasy(event.y)
                actual_y = int(canvas_y / scale_factor)

                # 限制在图片范围内
                if 10 < actual_y < height - 10:
                    # 检查是否与其他分割线冲突
                    conflict = False
                    for line_id, line_data in split_lines.items():
                        if line_id != selected_line and abs(actual_y - line_data['y']) < 20:
                            conflict = True
                            break

                    if not conflict:
                        # 更新分割线位置
                        old_y = split_lines[selected_line]['y']
                        split_lines[selected_line]['y'] = actual_y
                        split_lines[selected_line]['canvas_y'] = canvas_y

                        # 更新split_points列表
                        if old_y in split_points:
                            split_points.remove(old_y)
                        split_points.append(actual_y)
                        split_points.sort()

                        # 移动线条
                        canvas.coords(selected_line, 0, canvas_y, display_width, canvas_y)

                        # 移动文本
                        text_id = split_lines[selected_line]['text_id']
                        canvas.coords(text_id, 10, canvas_y - 15)
                        canvas.itemconfig(text_id, text=f"Y:{actual_y}")

                        update_info()

        def on_release(event):
            """处理鼠标释放"""
            nonlocal selected_line, drag_start_y

            if selected_line:
                # 取消选中状态
                canvas.itemconfig(selected_line, fill='red', width=2)
                selected_line = None
                drag_start_y = None

        def on_double_click(event):
            """处理双击删除分割线"""
            canvas_y = canvas.canvasy(event.y)

            # 查找最近的分割线
            closest_line = None
            min_distance = float('inf')

            for line_id, line_data in split_lines.items():
                distance = abs(canvas_y - line_data['canvas_y'])
                if distance < 15 and distance < min_distance:
                    closest_line = line_id
                    min_distance = distance

            if closest_line:
                # 删除分割线
                line_data = split_lines[closest_line]
                split_points.remove(line_data['y'])
                canvas.delete(closest_line)
                canvas.delete(line_data['text_id'])
                del split_lines[closest_line]
                update_info()

        def remove_selected_split():
            """删除选中的分割点"""
            nonlocal selected_line

            if selected_line:
                line_data = split_lines[selected_line]
                split_points.remove(line_data['y'])
                canvas.delete(selected_line)
                canvas.delete(line_data['text_id'])
                del split_lines[selected_line]
                selected_line = None
                update_info()
            else:
                messagebox.showinfo("提示", "请先点击选择一个分割线")

        def clear_all_splits():
            """清除所有分割点"""
            nonlocal selected_line

            split_points.clear()
            for line_id, line_data in split_lines.items():
                canvas.delete(line_id)
                canvas.delete(line_data['text_id'])
            split_lines.clear()
            selected_line = None
            update_info()

        def update_info():
            """更新信息显示"""
            info_text = f"分割点数量: {len(split_points)}\n"
            if split_points:
                sorted_points = sorted(split_points)
                info_text += f"分割点位置: {sorted_points}\n"
                info_text += f"将产生 {len(split_points) + 1} 个片段\n"
                if selected_line:
                    selected_y = split_lines[selected_line]['y']
                    info_text += f"选中分割点: Y={selected_y}"
            else:
                info_text += "点击图片添加分割点\n"
                info_text += "拖拽分割线可移动位置\n"
                info_text += "双击分割线可删除"
            info_label.config(text=info_text)

        def finish_splitting():
            """完成分割"""
            if not split_points:
                messagebox.showwarning("警告", "请至少添加一个分割点")
                return

            result = messagebox.askyesno("确认", f"确认使用 {len(split_points)} 个分割点进行分割？")
            if result:
                root.quit()

        def auto_suggest_splits():
            """自动建议分割点"""
            try:
                segment_count = simpledialog.askinteger("自动建议", "建议分割成几段？",
                                                      initialvalue=5, minvalue=2, maxvalue=20)
                if segment_count:
                    clear_all_splits()
                    segment_height = height // segment_count

                    for i in range(1, segment_count):
                        y = i * segment_height
                        canvas_y = y * scale_factor
                        add_split_point(canvas_y)
                    update_info()
            except:
                pass

        # 控制面板
        control_frame = tk.Frame(root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Button(control_frame, text="删除选中分割点", command=remove_selected_split).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="清除所有分割点", command=clear_all_splits).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="自动建议分割点", command=auto_suggest_splits).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="完成分割", command=finish_splitting, bg='green', fg='white').pack(side=tk.RIGHT, padx=5)

        # 信息显示
        info_label = tk.Label(root, text="", justify=tk.LEFT, bg='lightgray', font=('Arial', 10))
        info_label.pack(fill=tk.X, padx=10, pady=5)

        # 绑定事件
        canvas.bind("<Button-1>", on_click)
        canvas.bind("<B1-Motion>", on_drag)
        canvas.bind("<ButtonRelease-1>", on_release)
        canvas.bind("<Double-Button-1>", on_double_click)

        # 显示使用说明
        help_text = ("使用说明：\n"
                    "• 单击空白处：添加分割点\n"
                    "• 单击分割线：选中分割线（变蓝色）\n"
                    "• 拖拽分割线：移动分割点位置\n"
                    "• 双击分割线：删除分割点\n"
                    "• 分割线最小间距：20像素")
        tk.Label(root, text=help_text, justify=tk.LEFT, fg='blue', font=('Arial', 9)).pack(padx=10, pady=5)

        update_info()

        # 运行GUI
        root.mainloop()
        root.destroy()

        # 处理分割结果
        if not split_points:
            print("❌ 未设置分割点，使用自动分割")
            return self.split_by_aspect_ratio(img, base_name)

        return self.create_manual_segments(img, base_name, split_points)

    def split_manually_cli(self, img, base_name):
        """命令行模式手动分割"""
        width, height = img.size
        split_points = []

        print("\n📋 命令行手动分割模式")
        print(f"图片高度: {height}px")
        print("输入分割点的Y坐标（像素），输入'done'完成，输入'help'查看帮助")

        # 创建预览图片（可选）
        try:
            preview_height = min(100, height // 10)
            preview_img = img.resize((width // 10, preview_height), self.deps['Image'].Resampling.LANCZOS)
            preview_path = self.temp_dir / f"{base_name}_preview.png"
            preview_img.save(preview_path)
            print(f"💡 已生成预览图: {preview_path}")
        except:
            pass

        while True:
            try:
                user_input = input(f"\n当前分割点: {sorted(split_points)}\n请输入Y坐标 (0-{height}): ").strip().lower()

                if user_input == 'done':
                    break
                elif user_input == 'help':
                    print("\n帮助信息:")
                    print("- 输入数字: 添加分割点（Y坐标）")
                    print("- 输入'remove X': 删除坐标为X的分割点")
                    print("- 输入'clear': 清除所有分割点")
                    print("- 输入'auto N': 自动生成N个等间距分割点")
                    print("- 输入'done': 完成分割")
                    continue
                elif user_input == 'clear':
                    split_points.clear()
                    print("✅ 已清除所有分割点")
                    continue
                elif user_input.startswith('remove '):
                    try:
                        y_to_remove = int(user_input.split()[1])
                        if y_to_remove in split_points:
                            split_points.remove(y_to_remove)
                            print(f"✅ 已删除分割点: {y_to_remove}")
                        else:
                            print(f"❌ 分割点 {y_to_remove} 不存在")
                    except:
                        print("❌ 格式错误，请使用: remove 坐标")
                    continue
                elif user_input.startswith('auto '):
                    try:
                        segment_count = int(user_input.split()[1])
                        if 2 <= segment_count <= 20:
                            split_points.clear()
                            segment_height = height // segment_count
                            for i in range(1, segment_count):
                                split_points.append(i * segment_height)
                            print(f"✅ 已自动生成 {len(split_points)} 个分割点")
                        else:
                            print("❌ 分割数量应在2-20之间")
                    except:
                        print("❌ 格式错误，请使用: auto 数量")
                    continue

                # 尝试解析为数字
                y_coord = int(user_input)
                if 0 < y_coord < height:
                    if y_coord not in split_points:
                        split_points.append(y_coord)
                        split_points.sort()
                        print(f"✅ 已添加分割点: {y_coord}")
                    else:
                        print(f"⚠️ 分割点 {y_coord} 已存在")
                else:
                    print(f"❌ 坐标必须在 1-{height-1} 之间")

            except ValueError:
                print("❌ 请输入有效的数字或命令")
            except KeyboardInterrupt:
                print("\n❌ 用户取消操作")
                return self.split_by_aspect_ratio(img, base_name)

        if not split_points:
            print("❌ 未设置分割点，使用自动分割")
            return self.split_by_aspect_ratio(img, base_name)

        return self.create_manual_segments(img, base_name, split_points)

    def create_manual_segments(self, img, base_name, split_points):
        """根据手动分割点创建片段"""
        width, height = img.size
        split_points = sorted(split_points)

        print(f"\n📊 开始按分割点分割图片...")
        print(f"分割点: {split_points}")
        print(f"将产生 {len(split_points) + 1} 个片段")

        pages_data = []

        # 添加起始点和结束点
        all_points = [0] + split_points + [height]

        for i in range(len(all_points) - 1):
            y_start = all_points[i]
            y_end = all_points[i + 1]

            # 确保片段有足够的高度
            if y_end - y_start < 50:  # 最小高度50像素
                print(f"⚠️ 片段 {i+1} 高度太小 ({y_end - y_start}px)，跳过")
                continue

            # 裁剪图片
            cropped = img.crop((0, y_start, width, y_end))

            pages_data.append({
                'page_num': len(pages_data) + 1,
                'image': cropped,
                'original_text': f"{base_name} - 第{len(pages_data) + 1}段 (Y:{y_start}-{y_end})",
                'script_text': '',
                'use_audio': True
            })

            print(f"  ✅ 片段 {len(pages_data)}: Y坐标 {y_start}-{y_end} (高度: {y_end - y_start}px)")

        print(f"✅ 手动分割完成，共 {len(pages_data)} 个片段")
        return pages_data

    def smart_resize(self, img, target_size):
        """智能调整图片尺寸，保持比例并添加合适的背景"""
        target_width, target_height = target_size
        img_width, img_height = img.size

        # 计算缩放比例
        scale_w = target_width / img_width
        scale_h = target_height / img_height
        scale = min(scale_w, scale_h)

        # 计算新尺寸
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)

        # 缩放图片
        img_scaled = img.resize((new_width, new_height), self.deps['Image'].Resampling.LANCZOS)

        # 创建目标尺寸的背景
        # 根据图片内容选择背景色
        background_color = self.get_dominant_color(img)
        result = self.deps['Image'].new('RGB', target_size, background_color)

        # 居中粘贴
        x = (target_width - new_width) // 2
        y = (target_height - new_height) // 2
        result.paste(img_scaled, (x, y))

        return result

    def crop_to_aspect_ratio(self, img, target_ratio):
        """裁剪图片到指定宽高比"""
        img_width, img_height = img.size
        img_ratio = img_width / img_height

        if img_ratio > target_ratio:
            # 图片太宽，裁剪宽度
            new_width = int(img_height * target_ratio)
            x = (img_width - new_width) // 2
            cropped = img.crop((x, 0, x + new_width, img_height))
        else:
            # 图片太高，裁剪高度
            new_height = int(img_width / target_ratio)
            y = (img_height - new_height) // 2
            cropped = img.crop((0, y, img_width, y + new_height))

        # 缩放到目标尺寸
        return cropped.resize((1280, 720), self.deps['Image'].Resampling.LANCZOS)

    def stretch_to_size(self, img, target_size):
        """直接拉伸到目标尺寸"""
        return img.resize(target_size, self.deps['Image'].Resampling.LANCZOS)

    def scale_with_letterbox(self, img, target_size):
        """等比缩放并添加黑边"""
        target_width, target_height = target_size
        img_width, img_height = img.size

        # 计算缩放比例
        scale_w = target_width / img_width
        scale_h = target_height / img_height
        scale = min(scale_w, scale_h)

        # 计算新尺寸
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)

        # 缩放图片
        img_scaled = img.resize((new_width, new_height), self.deps['Image'].Resampling.LANCZOS)

        # 创建黑色背景
        result = self.deps['Image'].new('RGB', target_size, (0, 0, 0))

        # 居中粘贴
        x = (target_width - new_width) // 2
        y = (target_height - new_height) // 2
        result.paste(img_scaled, (x, y))

        return result

    def get_dominant_color(self, img):
        """获取图片的主要颜色作为背景色"""
        try:
            # 缩小图片以提高处理速度
            small_img = img.resize((50, 50))

            # 获取边缘像素的平均颜色
            pixels = []
            width, height = small_img.size

            # 采样边缘像素
            for x in range(width):
                pixels.append(small_img.getpixel((x, 0)))  # 上边
                pixels.append(small_img.getpixel((x, height-1)))  # 下边
            for y in range(height):
                pixels.append(small_img.getpixel((0, y)))  # 左边
                pixels.append(small_img.getpixel((width-1, y)))  # 右边

            # 计算平均颜色
            if pixels:
                avg_r = sum(p[0] for p in pixels) // len(pixels)
                avg_g = sum(p[1] for p in pixels) // len(pixels)
                avg_b = sum(p[2] for p in pixels) // len(pixels)

                # 稍微调暗作为背景色
                return (max(0, avg_r - 20), max(0, avg_g - 20), max(0, avg_b - 20))
            else:
                return (240, 240, 240)  # 默认浅灰色
        except:
            return (240, 240, 240)  # 默认浅灰色

    def add_segment_label(self, img, page_num):
        """添加段号标识"""
        img_copy = img.copy()
        draw = self.deps['ImageDraw'].Draw(img_copy)

        try:
            font = self.deps['ImageFont'].truetype("arial.ttf", 24)
        except:
            font = self.deps['ImageFont'].load_default()

        # 添加半透明背景
        from PIL import Image as PILImage
        overlay = PILImage.new('RGBA', (200, 40), (0, 0, 0, 128))
        img_copy.paste(overlay, (10, 10), overlay)

        draw.text((15, 20), f"第 {page_num} 段", fill=(255, 255, 255), font=font)

        return img_copy
    
    def generate_initial_scripts(self, pages_data, file_name):
        """生成初始文案"""
        print("\n📝 生成初始文案...")
        
        # 创建文案文件
        scripts_filename = f"{file_name}_scripts.txt"
        scripts_path = Path(scripts_filename)
        
        # 检查是否已存在文案文件
        if scripts_path.exists():
            print(f"✅ 发现现有文案文件: {scripts_filename}")
            choice = input("是否使用现有文案？(y/n): ").lower()
            if choice == 'y':
                return self.load_scripts_from_file(pages_data, scripts_path)
        
        # 生成新文案
        print("正在生成文案...")
        with open(scripts_path, 'w', encoding='utf-8') as f:
            f.write(f"# {file_name} - 视频文案\n")
            f.write("# 格式：每段一行，以 [段号] 开头\n")
            f.write("# 示例：[1] 这是第一段的讲解内容\n")
            f.write("# 空行或以#开头的行将被忽略\n\n")
            
            for page_data in pages_data:
                page_num = page_data['page_num']
                original_text = page_data['original_text']
                
                # 处理文本：清理和优化
                cleaned_text = self.clean_text_for_speech(original_text)
                
                f.write(f"[{page_num}] {cleaned_text}\n\n")
                
                # 同时更新页面数据
                page_data['script_text'] = cleaned_text
        
        print(f"✅ 文案已生成到: {scripts_filename}")
        self.scripts_file = scripts_path
        return pages_data
    
    def clean_text_for_speech(self, text):
        """清理文本，使其更适合语音合成"""
        if not text or len(text.strip()) < 5:
            return "现在我们来看这部分内容。"
        
        # 移除多余的空白字符
        text = ' '.join(text.split())
        
        # 如果是自动生成的段落描述，增加更多内容
        if "段" in text and len(text) < 20:
            parts = text.split(" - ")
            if len(parts) > 1:
                return f"接下来我们来看{parts[1]}的详细内容。请大家仔细观察屏幕上显示的信息。"
        
        # 限制长度
        if len(text) > 300:
            # 尝试在句号处截断
            sentences = text.split('。')
            result = ""
            for sentence in sentences:
                if len(result + sentence + '。') <= 300:
                    result += sentence + '。'
                else:
                    break
            if result:
                text = result
            else:
                text = text[:300] + "..."
        
        # 添加适当的停顿标记
        text = text.replace('。', '。 ')
        text = text.replace('，', '， ')
        
        return text.strip()
    
    def load_scripts_from_file(self, pages_data, scripts_path):
        """从文件加载文案"""
        try:
            with open(scripts_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 解析文案
            scripts_dict = {}
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # 查找页码标记 [数字]
                if line.startswith('[') and ']' in line:
                    try:
                        end_bracket = line.index(']')
                        page_num = int(line[1:end_bracket])
                        script_text = line[end_bracket + 1:].strip()
                        scripts_dict[page_num] = script_text
                    except (ValueError, IndexError):
                        continue
            
            # 应用到页面数据
            for page_data in pages_data:
                page_num = page_data['page_num']
                if page_num in scripts_dict:
                    page_data['script_text'] = scripts_dict[page_num]
                else:
                    page_data['script_text'] = page_data['original_text']
            
            print(f"✅ 成功加载 {len(scripts_dict)} 段文案")
            return pages_data
            
        except Exception as e:
            print(f"❌ 文案加载失败: {e}")
            return pages_data
    
    def preview_and_edit_scripts(self, pages_data):
        """预览和编辑文案"""
        print("\n" + "="*60)
        print("📋 文案预览和编辑")
        print("="*60)
        
        # 显示所有文案预览
        print("\n当前文案预览:")
        print("-" * 40)
        for page_data in pages_data:
            page_num = page_data['page_num']
            script = page_data['script_text']
            print(f"[{page_num}] {script[:80]}{'...' if len(script) > 80 else ''}")
        
        print("\n选择操作:")
        print("1. 使用当前文案（继续生成视频）")
        print("2. 打开文案文件进行编辑")
        print("3. 重新生成文案")
        print("4. 批量修改文案")
        print("5. 设置无语音模式")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            print("✅ 使用当前文案")
            return pages_data
            
        elif choice == "2":
            if self.scripts_file and self.scripts_file.exists():
                print(f"\n📝 请编辑文案文件: {self.scripts_file}")
                print("编辑完成后按回车继续...")
                input()
                # 重新加载文案
                return self.load_scripts_from_file(pages_data, self.scripts_file)
            else:
                print("❌ 文案文件不存在")
                return pages_data
                
        elif choice == "3":
            print("重新生成文案...")
            return self.regenerate_scripts(pages_data)
            
        elif choice == "4":
            return self.batch_modify_scripts(pages_data)
            
        elif choice == "5":
            for page_data in pages_data:
                page_data['use_audio'] = False
                page_data['script_text'] = f"第{page_data['page_num']}段"
            print("✅ 设置为无语音模式")
            return pages_data
            
        else:
            print("使用当前文案")
            return pages_data
    
    def regenerate_scripts(self, pages_data):
        """重新生成文案"""
        print("选择文案生成方式:")
        print("1. 使用原始描述文本")
        print("2. 使用简化描述")
        print("3. 使用模板格式")
        print("4. 长图专用模板")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "2":
            # 简化描述
            for page_data in pages_data:
                page_data['script_text'] = f"现在我们来看第{page_data['page_num']}部分的内容。"
        elif choice == "3":
            # 模板格式
            template = input("输入模板（用{page}表示段号）: ")
            for page_data in pages_data:
                page_data['script_text'] = template.replace("{page}", str(page_data['page_num']))
        elif choice == "4":
            # 长图专用模板
            templates = [
                "让我们继续往下看，这里是第{page}部分。",
                "接下来我们来看第{page}段的详细内容。",
                "现在我们滚动到第{page}部分，请注意观察。",
                "这是第{page}段，让我为大家详细解读。"
            ]
            for i, page_data in enumerate(pages_data):
                template = templates[i % len(templates)]
                page_data['script_text'] = template.replace("{page}", str(page_data['page_num']))
        else:
            # 使用原始文本
            for page_data in pages_data:
                page_data['script_text'] = self.clean_text_for_speech(page_data['original_text'])
        
        # 更新文案文件
        if self.scripts_file:
            self.save_scripts_to_file(pages_data)
        
        return pages_data
    
    def batch_modify_scripts(self, pages_data):
        """批量修改文案"""
        print("批量修改选项:")
        print("1. 添加统一前缀")
        print("2. 添加统一后缀")
        print("3. 替换特定文字")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            prefix = input("输入前缀: ")
            for page_data in pages_data:
                page_data['script_text'] = f"{prefix} {page_data['script_text']}"
        elif choice == "2":
            suffix = input("输入后缀: ")
            for page_data in pages_data:
                page_data['script_text'] = f"{page_data['script_text']} {suffix}"
        elif choice == "3":
            old_text = input("要替换的文字: ")
            new_text = input("替换为: ")
            for page_data in pages_data:
                page_data['script_text'] = page_data['script_text'].replace(old_text, new_text)
        
        # 更新文案文件
        if self.scripts_file:
            self.save_scripts_to_file(pages_data)
        
        return pages_data
    
    def save_scripts_to_file(self, pages_data):
        """保存文案到文件"""
        if not self.scripts_file:
            return
        
        try:
            with open(self.scripts_file, 'w', encoding='utf-8') as f:
                f.write(f"# 视频文案 - 已更新\n")
                f.write("# 格式：每段一行，以 [段号] 开头\n\n")
                
                for page_data in pages_data:
                    page_num = page_data['page_num']
                    script_text = page_data['script_text']
                    f.write(f"[{page_num}] {script_text}\n\n")
            
            print(f"✅ 文案已更新到: {self.scripts_file}")
        except Exception as e:
            print(f"❌ 文案保存失败: {e}")
    
    def setup_voice_engine(self):
        """设置语音引擎"""
        print("\n🎤 语音设置")
        print("=" * 20)

        use_voice = input("是否使用语音讲解？(y/n): ").lower()
        if use_voice != 'y':
            print("✅ 设置为无语音模式")
            return None

        print("\n选择语音引擎:")
        engines = []

        if self.deps['edge_tts']:
            engines.append(('edge', 'Edge-TTS (微软) - 高质量自然语音'))

        if self.deps['pyttsx3']:
            engines.append(('pyttsx3', 'pyttsx3 (本地) - 快速离线语音'))

        if not engines:
            print("❌ 没有可用的语音引擎")
            return None

        for i, (key, name) in enumerate(engines, 1):
            print(f"{i}. {name}")

        try:
            choice = int(input(f"请选择 (1-{len(engines)}): ")) - 1
            if 0 <= choice < len(engines):
                self.voice_engine = engines[choice][0]
                print(f"✅ 已选择: {engines[choice][1]}")

                # 如果选择了Edge-TTS，进一步选择语音
                if self.voice_engine == 'edge':
                    self.setup_edge_voice()

                return self.voice_engine
        except ValueError:
            pass

        # 默认选择第一个
        self.voice_engine = engines[0][0]
        if self.voice_engine == 'edge':
            self.setup_edge_voice()
        print(f"✅ 使用默认引擎: {engines[0][1]}")
        return self.voice_engine

    def setup_edge_voice(self):
        """设置Edge-TTS语音"""
        print("\n🎭 选择Edge-TTS语音:")

        # 中文语音选项 - 按稳定性排序
        voices = [
            ('zh-CN-XiaoxiaoNeural', '晓晓 (女声) - 温柔甜美 ⭐推荐'),
            ('zh-CN-YunxiNeural', '云希 (男声) - 成熟稳重 ⭐推荐'),
            ('zh-CN-YunyangNeural', '云扬 (男声) - 专业播音 ⭐推荐'),
            ('zh-CN-XiaoyiNeural', '晓伊 (女声) - 清新自然'),
            ('zh-CN-YunjianNeural', '云健 (男声) - 活力阳光'),
            ('zh-CN-XiaochenNeural', '晓辰 (女声) - 知性优雅'),
            ('zh-CN-XiaohanNeural', '晓涵 (女声) - 亲切温暖'),
            ('zh-CN-XiaomengNeural', '晓梦 (女声) - 可爱活泼'),
            ('zh-CN-XiaomoNeural', '晓墨 (女声) - 成熟知性'),
            ('zh-CN-XiaoqiuNeural', '晓秋 (女声) - 温和亲和'),
            ('zh-CN-XiaoruiNeural', '晓睿 (女声) - 聪慧理性'),
            ('zh-CN-XiaoshuangNeural', '晓双 (女声) - 清脆明亮'),
            ('zh-CN-XiaoxuanNeural', '晓萱 (女声) - 优雅大方'),
            ('zh-CN-XiaoyanNeural', '晓颜 (女声) - 甜美可人'),
            ('zh-CN-XiaoyouNeural', '晓悠 (女声) - 悠扬动听'),
            ('zh-CN-XiaozhenNeural', '晓甄 (女声) - 端庄典雅'),
            ('zh-CN-YunfengNeural', '云枫 (男声) - 磁性深沉'),
            ('zh-CN-YunhaoNeural', '云皓 (男声) - 清朗有力'),
            ('zh-CN-YunxiaNeural', '云夏 (男声) - 热情洋溢'),
            ('zh-CN-YunyeNeural', '云野 (男声) - 自然随性')
        ]

        # 检查是否要测试语音可用性
        print("\n选择模式:")
        print("1. 直接选择语音（快速）")
        print("2. 测试语音可用性后选择（推荐）")

        mode_choice = input("请选择模式 (1-2): ").strip()

        if mode_choice == "2":
            print("\n🧪 正在测试语音可用性...")
            voices = self.test_voice_availability(voices)

        print("女声选项:")
        female_voices = [(i, v) for i, v in enumerate(voices) if '女声' in v[1]]
        for i, (idx, (voice_id, desc)) in enumerate(female_voices, 1):
            print(f"  {i}. {desc}")

        print("\n男声选项:")
        male_voices = [(i, v) for i, v in enumerate(voices) if '男声' in v[1]]
        for i, (idx, (voice_id, desc)) in enumerate(male_voices, len(female_voices) + 1):
            print(f"  {i}. {desc}")

        print(f"\n0. 使用默认语音 (晓晓)")

        try:
            choice = int(input(f"请选择语音 (0-{len(voices)}): "))
            if choice == 0:
                self.selected_voice = voices[0][0]  # 默认晓晓
                print(f"✅ 使用默认语音: {voices[0][1]}")
            elif 1 <= choice <= len(voices):
                self.selected_voice = voices[choice - 1][0]
                print(f"✅ 已选择语音: {voices[choice - 1][1]}")
            else:
                self.selected_voice = voices[0][0]  # 默认
                print(f"✅ 使用默认语音: {voices[0][1]}")
        except ValueError:
            self.selected_voice = voices[0][0]  # 默认
            print(f"✅ 使用默认语音: {voices[0][1]}")

        # 语音参数设置
        print(f"\n🎛️ 语音参数设置 (可选):")
        print("1. 使用默认参数")
        print("2. 自定义参数")

        param_choice = input("请选择 (1-2): ").strip()
        if param_choice == "2":
            self.setup_voice_parameters()

    def setup_voice_parameters(self):
        """设置语音参数"""
        print("\n语音参数设置:")

        # 语速设置
        try:
            rate = input("语速 (默认: +0%, 范围: -50% 到 +100%): ").strip()
            if not rate:
                rate = "+0%"
            elif not rate.endswith('%'):
                rate = f"+{rate}%" if not rate.startswith('-') else f"{rate}%"
            self.voice_rate = rate
        except:
            self.voice_rate = "+0%"

        # 音调设置
        try:
            pitch = input("音调 (默认: +0Hz, 范围: -200Hz 到 +200Hz): ").strip()
            if not pitch:
                pitch = "+0Hz"
            elif not pitch.endswith('Hz'):
                pitch = f"+{pitch}Hz" if not pitch.startswith('-') else f"{pitch}Hz"
            self.voice_pitch = pitch
        except:
            self.voice_pitch = "+0Hz"

        print(f"✅ 语音参数设置完成:")
        print(f"   语速: {self.voice_rate}")
        print(f"   音调: {self.voice_pitch}")

    def test_voice_availability(self, voices):
        """测试语音可用性"""
        import subprocess
        import tempfile

        available_voices = []
        test_text = "测试"

        print("正在测试语音，请稍候...")

        for voice_id, desc in voices:
            try:
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_path = temp_file.name

                cmd = [
                    "edge-tts",
                    "--voice", voice_id,
                    "--text", test_text,
                    "--write-media", temp_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                if result.returncode == 0 and Path(temp_path).exists():
                    file_size = Path(temp_path).stat().st_size
                    if file_size > 500:
                        available_voices.append((voice_id, f"{desc} ✅"))
                        print(f"  ✅ {desc}")
                    else:
                        print(f"  ❌ {desc} (文件太小)")
                else:
                    print(f"  ❌ {desc} (生成失败)")

                # 清理临时文件
                try:
                    Path(temp_path).unlink()
                except:
                    pass

            except subprocess.TimeoutExpired:
                print(f"  ⏰ {desc} (超时)")
            except Exception as e:
                print(f"  ❌ {desc} (异常: {e})")

        if not available_voices:
            print("⚠️ 没有可用的语音，使用原始列表")
            return voices

        print(f"\n✅ 找到 {len(available_voices)} 个可用语音")
        return available_voices
    
    def generate_speech(self, text, output_path):
        """生成语音"""
        if self.voice_engine == 'edge':
            return self.generate_edge_speech(text, output_path)
        elif self.voice_engine == 'pyttsx3':
            return self.generate_pyttsx3_speech(text, output_path)
        return False
    
    def generate_edge_speech(self, text, output_path):
        """使用Edge-TTS生成语音"""
        import subprocess
        import time

        # 清理和验证文本
        text = self.clean_text_for_tts(text)
        if not text:
            print("⚠️ 文本为空，跳过语音生成")
            return False

        # 使用选择的语音，如果没有选择则使用默认
        voice = self.selected_voice or "zh-CN-XiaoxiaoNeural"

        # 如果之前这个语音失败过，直接使用备用方案
        if hasattr(self, 'failed_voices') and voice in self.failed_voices:
            print(f"    ⚠️ 语音 {voice} 之前失败过，直接使用备用方案")
            return self.generate_edge_speech_fallback(text, output_path)

        # 首先尝试主要语音，失败后直接使用备用方案
        try:
            cmd = [
                "edge-tts",
                "--voice", voice,
                "--text", text,
                "--write-media", str(output_path)
            ]

            # 添加语音参数（如果设置了）
            if hasattr(self, 'voice_rate') and self.voice_rate != "+0%":
                cmd.extend(["--rate", self.voice_rate])

            if hasattr(self, 'voice_pitch') and self.voice_pitch != "+0Hz":
                cmd.extend(["--pitch", self.voice_pitch])

            print(f"    🎤 使用语音: {voice}")

            # 设置超时时间
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=25)

            if result.returncode == 0 and Path(output_path).exists():
                file_size = Path(output_path).stat().st_size
                if file_size > 1000:  # 确保文件不是空的
                    print(f"    ✅ 语音生成成功 ({file_size} bytes)")
                    return True
                else:
                    print(f"    ⚠️ 生成的音频文件太小 ({file_size} bytes)")

            # 处理错误信息
            if result.stderr and "NoAudioReceived" in result.stderr:
                print(f"    ⚠️ 未收到音频数据，尝试备用方案...")
                self.failed_voices.add(voice)  # 记录失败的语音
            elif result.stderr:
                print(f"    ⚠️ 语音生成问题，尝试备用方案...")
                self.failed_voices.add(voice)  # 记录失败的语音

        except subprocess.TimeoutExpired:
            print(f"    ⏰ 语音生成超时，尝试备用方案...")
            self.failed_voices.add(voice)  # 记录失败的语音
        except Exception as e:
            print(f"    ❌ 语音生成异常: {e}，尝试备用方案...")
            self.failed_voices.add(voice)  # 记录失败的语音

        # 主要语音失败，尝试备用方案
        return self.generate_edge_speech_fallback(text, output_path)

    def clean_text_for_tts(self, text):
        """清理文本用于TTS"""
        if not text:
            return ""

        # 移除特殊字符和控制字符
        import re
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s，。！？；：""''（）【】《》、]', '', text)

        # 移除多余空格
        text = ' '.join(text.split())

        # 限制长度
        if len(text) > 500:
            text = text[:500]

        # 确保文本不为空
        if not text.strip():
            return "这是一段内容。"

        return text.strip()

    def generate_edge_speech_fallback(self, text, output_path):
        """Edge-TTS备用方案"""
        import subprocess

        print("    🔄 使用备用语音方案...")

        # 使用最稳定的语音和简化文本
        fallback_voices = [
            "zh-CN-XiaoxiaoNeural",  # 最稳定的女声
            "zh-CN-YunxiNeural",     # 最稳定的男声
        ]

        # 简化文本，提高成功率
        if len(text) > 200:
            simple_text = text[:200] + "。"
        elif len(text) > 100:
            simple_text = text[:100] + "。"
        else:
            simple_text = text

        # 移除可能导致问题的字符
        simple_text = simple_text.replace('"', '').replace("'", '').replace('《', '').replace('》', '')

        for i, voice in enumerate(fallback_voices):
            try:
                print(f"    🎭 尝试备用语音 {i+1}: {voice}")

                cmd = [
                    "edge-tts",
                    "--voice", voice,
                    "--text", simple_text,
                    "--write-media", str(output_path)
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)

                if result.returncode == 0 and Path(output_path).exists():
                    file_size = Path(output_path).stat().st_size
                    if file_size > 500:
                        print(f"    ✅ 备用语音生成成功 ({voice}, {file_size} bytes)")
                        return True
                    else:
                        print(f"    ⚠️ 备用语音文件太小: {file_size} bytes")
                else:
                    print(f"    ❌ 备用语音 {voice} 失败")

            except subprocess.TimeoutExpired:
                print(f"    ⏰ 备用语音 {voice} 超时")
            except Exception as e:
                print(f"    ❌ 备用语音 {voice} 异常: {e}")

        print("    ❌ 所有备用语音方案都失败")
        return False
    
    def generate_pyttsx3_speech(self, text, output_path):
        """使用pyttsx3生成语音"""
        try:
            engine = self.deps['pyttsx3'].init()
            engine.setProperty('rate', 160)
            engine.setProperty('volume', 0.9)
            
            # 选择中文语音
            voices = engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'mandarin' in voice.name.lower():
                    engine.setProperty('voice', voice.id)
                    break
            
            engine.save_to_file(text, str(output_path))
            engine.runAndWait()
            
            # 等待文件生成
            timeout = 15
            while not Path(output_path).exists() and timeout > 0:
                time.sleep(0.5)
                timeout -= 1
            
            return Path(output_path).exists()
        except Exception as e:
            print(f"pyttsx3生成失败: {e}")
            return False
    
    def create_video(self, pages_data, output_path):
        """创建视频"""
        print("\n🎬 开始创建视频...")

        # 选择图片处理方式
        print("\n选择图片处理方式:")
        print("1. 智能适配（推荐）- 保持比例，添加背景")
        print("2. 居中裁剪 - 裁剪到16:9比例")
        print("3. 拉伸填充 - 强制拉伸到16:9（可能畸变）")
        print("4. 等比缩放 - 保持原比例，上下留黑边")

        choice = input("请选择 (1-4): ").strip()

        clips = []

        for page_data in pages_data:
            page_num = page_data['page_num']
            print(f"  处理第 {page_num} 段...")

            # 处理图片
            img = page_data['image']

            if choice == "2":
                img_processed = self.crop_to_aspect_ratio(img, 16/9)
            elif choice == "3":
                img_processed = self.stretch_to_size(img, (1280, 720))
            elif choice == "4":
                img_processed = self.scale_with_letterbox(img, (1280, 720))
            else:  # 默认智能适配
                img_processed = self.smart_resize(img, (1280, 720))

            # 添加段号标识
            img_processed = self.add_segment_label(img_processed, page_num)

            # 保存图片
            img_path = self.temp_dir / f"segment_{page_num}.png"
            img_processed.save(img_path)
            
            # 处理音频
            duration = 5
            audio_path = None

            if page_data['use_audio'] and self.voice_engine:
                audio_path = self.temp_dir / f"audio_{page_num}.wav"
                script_text = page_data['script_text']

                print(f"    🎵 生成语音: {script_text[:30]}...")

                if self.generate_speech(script_text, str(audio_path)):
                    try:
                        audio_clip = self.deps['AudioFileClip'](str(audio_path))
                        duration = max(audio_clip.duration, 3)
                        audio_clip.close()
                        print(f"    ✅ 语音时长: {duration:.1f}秒")
                    except Exception as e:
                        print(f"    ⚠️ 音频文件读取失败: {e}")
                        duration = 5  # 使用默认时长
                        audio_path = None
                else:
                    print(f"    ⚠️ 语音生成失败，使用静音视频")
                    audio_path = None
                    duration = 5  # 使用默认时长
            
            # 创建视频片段
            try:
                video_clip = self.deps['ImageClip'](str(img_path), duration=duration)
                
                if audio_path and Path(audio_path).exists():
                    audio_clip = self.deps['AudioFileClip'](str(audio_path))
                    video_clip = video_clip.set_audio(audio_clip)
                
                clips.append(video_clip)
                print(f"    ✅ 第 {page_num} 段完成 (时长: {duration:.1f}秒)")
            except Exception as e:
                print(f"    ❌ 第 {page_num} 段失败: {e}")
                continue
        
        if not clips:
            print("❌ 没有成功创建任何视频片段")
            return False
        
        try:
            # 合并视频
            print("正在合并视频...")
            final_video = self.deps['concatenate_videoclips'](clips)
            
            print("正在输出视频文件...")
            final_video.write_videofile(
                str(output_path),
                fps=24,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            # 清理
            final_video.close()
            for clip in clips:
                clip.close()
            
            return True
        except Exception as e:
            print(f"❌ 视频生成失败: {e}")
            return False
    
    def convert(self, file_path):
        """转换文件为视频"""
        file_path = Path(file_path)
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return False
        
        if not self.is_supported_file(file_path):
            print(f"❌ 不支持的文件格式: {file_path.suffix}")
            print(f"支持的格式: {', '.join(self.supported_formats)}")
            return False
        
        file_name = file_path.stem
        file_type = "PDF" if file_path.suffix.lower() == '.pdf' else "图片"
        print(f"🎯 开始处理{file_type}: {file_path}")
        print("="*60)
        
        try:
            # 1. 提取内容
            pages_data = self.extract_content(file_path)
            if not pages_data:
                return False
            
            # 2. 生成初始文案
            pages_data = self.generate_initial_scripts(pages_data, file_name)
            
            # 3. 预览和编辑文案
            pages_data = self.preview_and_edit_scripts(pages_data)
            
            # 4. 设置语音引擎
            self.setup_voice_engine()
            
            # 5. 最终确认
            print(f"\n📋 最终配置:")
            audio_pages = sum(1 for p in pages_data if p['use_audio'])
            print(f"总段数: {len(pages_data)}")
            print(f"语音段数: {audio_pages}")
            print(f"语音引擎: {self.voice_engine or '无'}")
            
            confirm = input("\n确认开始生成视频？(y/n): ").lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                return False
            
            # 6. 创建视频
            output_name = f"{file_name}_video.mp4"
            output_path = self.output_dir / output_name
            
            success = self.create_video(pages_data, output_path)
            
            if success:
                print(f"\n🎉 转换成功!")
                print(f"输出文件: {output_path}")
                print(f"文案文件: {self.scripts_file}")
                if output_path.exists():
                    size_mb = output_path.stat().st_size / 1024 / 1024
                    print(f"文件大小: {size_mb:.1f} MB")
                return True
            else:
                print("\n❌ 转换失败")
                return False
                
        except Exception as e:
            print(f"❌ 转换过程出错: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
        except:
            pass

def find_supported_files():
    """查找当前目录下支持的文件"""
    current_dir = Path(".")
    supported_formats = {'.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    
    files = []
    for format_ext in supported_formats:
        files.extend(current_dir.glob(f"*{format_ext}"))
        files.extend(current_dir.glob(f"*{format_ext.upper()}"))
    
    return files

def main():
    print("长图转视频工具 - 增强版")
    print("=" * 30)
    print("✨ 支持PDF和长图转视频")
    print("✨ 支持文案生成、预览、编辑")
    print("✨ 支持多种分割方式和语音合成")
    print("=" * 30)
    
    # 检查依赖
    deps = check_dependencies()
    if not deps:
        return
    
    # 获取文件
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        supported_files = find_supported_files()
        if not supported_files:
            print("❌ 当前目录下没有找到支持的文件")
            print("支持的格式: PDF, JPG, PNG, BMP, TIFF, WEBP")
            return
        elif len(supported_files) == 1:
            file_path = supported_files[0]
        else:
            print("找到多个支持的文件:")
            for i, file_path in enumerate(supported_files, 1):
                file_size = file_path.stat().st_size / 1024 / 1024
                print(f"{i}. {file_path} ({file_size:.1f} MB)")
            
            try:
                choice = int(input("请选择: ")) - 1
                file_path = supported_files[choice]
            except:
                print("❌ 选择错误")
                return
    
    # 开始转换
    converter = EnhancedImageVideoConverter(deps)
    converter.convert(file_path)

if __name__ == "__main__":
    main()