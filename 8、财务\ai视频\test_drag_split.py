#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分割线拖拽功能
"""

import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_image():
    """创建测试图片"""
    width = 600
    height = 1200
    
    # 创建图片
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    # 绘制网格和标记
    colors = ['lightblue', 'lightgreen', 'lightyellow', 'lightpink']
    section_height = height // 4
    
    for i in range(4):
        y_start = i * section_height
        y_end = (i + 1) * section_height
        
        # 填充背景色
        draw.rectangle([0, y_start, width, y_end], fill=colors[i])
        
        # 绘制边框
        draw.rectangle([0, y_start, width-1, y_end-1], outline='black', width=2)
        
        # 添加区域标识
        text = f"区域 {i+1}"
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        text_x = (width - text_width) // 2
        text_y = y_start + (section_height - text_height) // 2
        
        draw.text((text_x, text_y), text, fill='black', font=font)
        
        # 添加Y坐标标记
        for j in range(0, section_height, 50):
            y_pos = y_start + j
            if y_pos < height:
                draw.line([0, y_pos, 20, y_pos], fill='red', width=1)
                draw.text((25, y_pos - 8), f"Y={y_pos}", fill='red', font=ImageFont.load_default())
    
    # 添加标题
    title = "拖拽测试图片"
    title_bbox = draw.textbbox((0, 0), title, font=font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((width - title_width) // 2, 10), title, fill='red', font=font)
    
    return img

def test_drag_functionality():
    """测试拖拽功能"""
    print("创建测试图片...")
    test_img = create_test_image()
    
    print(f"测试图片尺寸: {test_img.width} x {test_img.height}")
    
    try:
        from gui_video_converter import ManualSplitDialog
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("打开手动分割对话框...")
        print("操作说明:")
        print("1. 点击图片空白处添加分割点")
        print("2. 点击分割线选中（变蓝色）")
        print("3. 拖拽分割线移动位置")
        print("4. 双击分割线删除")
        print("5. 完成后点击'完成分割'")
        
        # 创建手动分割对话框
        dialog = ManualSplitDialog(root, test_img, "拖拽测试")
        
        # 等待对话框关闭
        root.wait_window(dialog.dialog)
        
        # 检查结果
        if hasattr(dialog, 'split_result') and dialog.split_result:
            print(f"✅ 分割成功！")
            print(f"生成片段数: {len(dialog.split_result)}")
            
            for i, segment in enumerate(dialog.split_result):
                img = segment['image']
                print(f"  片段 {i+1}: {img.width}x{img.height}px")
        else:
            print("❌ 分割被取消或失败")
        
        root.destroy()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保gui_video_converter.py在同一目录下")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("分割线拖拽功能测试")
    print("=" * 30)
    
    print("功能说明:")
    print("✨ 单击空白处：添加分割点")
    print("✨ 单击分割线：选中分割线（变蓝色）")
    print("✨ 拖拽分割线：移动分割点位置")
    print("✨ 双击分割线：删除分割点")
    print("✨ 分割线最小间距：30像素")
    print()
    
    choice = input("是否开始测试？(y/n): ").lower()
    if choice == 'y':
        test_drag_functionality()
    else:
        print("测试取消")

if __name__ == "__main__":
    main()
