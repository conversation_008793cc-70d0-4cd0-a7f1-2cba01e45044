#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速语音测试工具 - 找出当前网络环境下可用的语音
"""

import subprocess
import tempfile
import time
from pathlib import Path

def test_single_voice(voice_id, voice_name, test_text="测试语音"):
    """测试单个语音"""
    try:
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        cmd = [
            "edge-tts",
            "--voice", voice_id,
            "--text", test_text,
            "--write-media", temp_path
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=8)
        end_time = time.time()
        
        success = False
        file_size = 0
        error_msg = ""
        
        if result.returncode == 0 and Path(temp_path).exists():
            file_size = Path(temp_path).stat().st_size
            if file_size > 500:
                success = True
            else:
                error_msg = f"文件太小({file_size}字节)"
        else:
            if "NoAudioReceived" in result.stderr:
                error_msg = "未收到音频数据"
            else:
                error_msg = "生成失败"
        
        # 清理临时文件
        try:
            Path(temp_path).unlink()
        except:
            pass
        
        return {
            'success': success,
            'file_size': file_size,
            'time': end_time - start_time,
            'error': error_msg
        }
        
    except subprocess.TimeoutExpired:
        return {'success': False, 'error': '超时', 'time': 8.0, 'file_size': 0}
    except Exception as e:
        return {'success': False, 'error': str(e), 'time': 0, 'file_size': 0}

def main():
    print("🧪 Edge-TTS 语音快速测试")
    print("=" * 40)
    
    # 语音列表（按推荐程度排序）
    voices = [
        ('zh-CN-XiaoxiaoNeural', '晓晓 (女声) - 温柔甜美'),
        ('zh-CN-YunxiNeural', '云希 (男声) - 成熟稳重'),
        ('zh-CN-YunyangNeural', '云扬 (男声) - 专业播音'),
        ('zh-CN-XiaoyiNeural', '晓伊 (女声) - 清新自然'),
        ('zh-CN-YunjianNeural', '云健 (男声) - 活力阳光'),
        ('zh-CN-XiaochenNeural', '晓辰 (女声) - 知性优雅'),
        ('zh-CN-XiaohanNeural', '晓涵 (女声) - 亲切温暖'),
        ('zh-CN-XiaomengNeural', '晓梦 (女声) - 可爱活泼'),
        ('zh-CN-XiaomoNeural', '晓墨 (女声) - 成熟知性'),
        ('zh-CN-XiaoqiuNeural', '晓秋 (女声) - 温和亲和'),
        ('zh-CN-XiaoruiNeural', '晓睿 (女声) - 聪慧理性'),
        ('zh-CN-XiaoshuangNeural', '晓双 (女声) - 清脆明亮'),
        ('zh-CN-XiaoxuanNeural', '晓萱 (女声) - 优雅大方'),
        ('zh-CN-XiaoyanNeural', '晓颜 (女声) - 甜美可人'),
        ('zh-CN-XiaoyouNeural', '晓悠 (女声) - 悠扬动听'),
        ('zh-CN-XiaozhenNeural', '晓甄 (女声) - 端庄典雅'),
        ('zh-CN-YunfengNeural', '云枫 (男声) - 磁性深沉'),
        ('zh-CN-YunhaoNeural', '云皓 (男声) - 清朗有力'),
        ('zh-CN-YunxiaNeural', '云夏 (男声) - 热情洋溢'),
        ('zh-CN-YunyeNeural', '云野 (男声) - 自然随性')
    ]
    
    print("选择测试模式:")
    print("1. 快速测试（仅测试推荐语音）")
    print("2. 完整测试（测试所有语音）")
    print("3. 测试特定语音")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        # 快速测试前5个推荐语音
        test_voices = voices[:5]
        print(f"\n🚀 快速测试 {len(test_voices)} 个推荐语音...")
    elif choice == "3":
        print(f"\n选择要测试的语音:")
        for i, (voice_id, desc) in enumerate(voices, 1):
            print(f"{i:2d}. {desc}")
        
        try:
            voice_choice = int(input(f"请选择 (1-{len(voices)}): ")) - 1
            if 0 <= voice_choice < len(voices):
                test_voices = [voices[voice_choice]]
            else:
                print("❌ 选择无效，使用默认语音")
                test_voices = [voices[0]]
        except ValueError:
            print("❌ 输入无效，使用默认语音")
            test_voices = [voices[0]]
    else:
        # 完整测试
        test_voices = voices
        print(f"\n🔍 完整测试 {len(test_voices)} 个语音...")
    
    # 自定义测试文本
    test_text = input("\n输入测试文本 (默认: '大家好，这是语音测试'): ").strip()
    if not test_text:
        test_text = "大家好，这是语音测试"
    
    print(f"\n开始测试，使用文本: '{test_text}'")
    print("-" * 60)
    
    successful_voices = []
    failed_voices = []
    
    for i, (voice_id, desc) in enumerate(test_voices, 1):
        print(f"[{i:2d}/{len(test_voices)}] 测试 {desc}")
        
        result = test_single_voice(voice_id, desc, test_text)
        
        if result['success']:
            print(f"    ✅ 成功 - {result['file_size']}字节, {result['time']:.1f}秒")
            successful_voices.append((voice_id, desc, result))
        else:
            print(f"    ❌ 失败 - {result['error']}")
            failed_voices.append((voice_id, desc, result['error']))
        
        # 短暂延迟避免请求过快
        if i < len(test_voices):
            time.sleep(0.3)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    if successful_voices:
        print(f"\n✅ 可用语音 ({len(successful_voices)} 个):")
        for voice_id, desc, result in successful_voices:
            print(f"  • {desc}")
            print(f"    ID: {voice_id}")
            print(f"    性能: {result['file_size']}字节, {result['time']:.1f}秒")
        
        print(f"\n🎯 推荐使用:")
        # 按性能排序，推荐最快的
        successful_voices.sort(key=lambda x: x[2]['time'])
        best_voice = successful_voices[0]
        print(f"  最佳选择: {best_voice[1]}")
        print(f"  语音ID: {best_voice[0]}")
    
    if failed_voices:
        print(f"\n❌ 不可用语音 ({len(failed_voices)} 个):")
        for voice_id, desc, error in failed_voices:
            print(f"  • {desc} - {error}")
    
    print(f"\n💡 建议:")
    if successful_voices:
        print("1. 在 long_image_to_video.py 中选择上述可用语音")
        print("2. 避免使用失败的语音以提高效率")
        if len(successful_voices) >= 3:
            print("3. 您的网络环境良好，大部分语音都可用")
        else:
            print("3. 网络环境可能不稳定，建议使用推荐的语音")
    else:
        print("1. 当前网络环境无法使用Edge-TTS")
        print("2. 建议检查网络连接或使用本地语音引擎")
        print("3. 可以尝试使用VPN或更换网络环境")

if __name__ == "__main__":
    main()
