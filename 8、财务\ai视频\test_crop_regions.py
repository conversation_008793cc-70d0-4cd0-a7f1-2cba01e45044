#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试区域裁剪功能
"""

import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_image_with_content():
    """创建包含内容的测试图片"""
    width = 800
    height = 1000
    
    # 创建图片
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体
    try:
        title_font = ImageFont.truetype("arial.ttf", 32)
        content_font = ImageFont.truetype("arial.ttf", 20)
        small_font = ImageFont.truetype("arial.ttf", 16)
    except:
        title_font = ImageFont.load_default()
        content_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 绘制标题区域
    draw.rectangle([0, 0, width, 80], fill='lightblue', outline='blue', width=2)
    draw.text((width//2 - 100, 25), "测试文档标题", fill='darkblue', font=title_font)
    
    # 绘制主要内容区域
    content_areas = [
        (100, 120, 700, 300, 'lightgreen', '主要内容区域 1'),
        (100, 320, 700, 500, 'lightyellow', '主要内容区域 2'),
        (100, 520, 700, 700, 'lightpink', '主要内容区域 3'),
        (100, 720, 700, 900, 'lightcyan', '主要内容区域 4')
    ]
    
    for x1, y1, x2, y2, color, title in content_areas:
        # 绘制内容区域
        draw.rectangle([x1, y1, x2, y2], fill=color, outline='black', width=2)
        
        # 添加标题
        draw.text((x1 + 20, y1 + 20), title, fill='black', font=content_font)
        
        # 添加一些示例内容
        for i in range(3):
            content_y = y1 + 60 + i * 30
            if content_y < y2 - 20:
                draw.text((x1 + 40, content_y), f"• 这是{title}的第{i+1}行内容", 
                         fill='darkgreen', font=small_font)
    
    # 绘制一些"干扰"区域（可能需要裁剪掉的）
    interference_areas = [
        (50, 150, 150, 200, 'red', '广告区域'),
        (650, 250, 750, 300, 'orange', '水印'),
        (50, 550, 150, 600, 'purple', '无关内容'),
        (650, 750, 750, 800, 'brown', '页脚信息')
    ]
    
    for x1, y1, x2, y2, color, label in interference_areas:
        draw.rectangle([x1, y1, x2, y2], fill=color, outline='darkred', width=2)
        draw.text((x1 + 5, y1 + 5), label, fill='white', font=small_font)
    
    # 添加坐标网格（帮助定位）
    for x in range(0, width, 100):
        draw.line([x, 0, x, height], fill='lightgray', width=1)
        if x > 0:
            draw.text((x - 10, 5), str(x), fill='gray', font=small_font)
    
    for y in range(0, height, 100):
        draw.line([0, y, width, y], fill='lightgray', width=1)
        if y > 0:
            draw.text((5, y - 15), str(y), fill='gray', font=small_font)
    
    return img

def test_crop_functionality():
    """测试裁剪功能"""
    print("创建测试图片...")
    test_img = create_test_image_with_content()
    
    print(f"测试图片尺寸: {test_img.width} x {test_img.height}")
    
    try:
        from gui_video_converter import ManualSplitDialog
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("打开手动分割对话框...")
        print("\n🎯 裁剪功能测试说明:")
        print("1. 首先添加一些分割点（点击图片空白处）")
        print("2. 点击'进入裁剪模式'按钮")
        print("3. 拖拽框选要排除的区域（如红色的广告区域）")
        print("4. 可以创建多个裁剪区域")
        print("5. 双击裁剪区域可以删除")
        print("6. 点击'退出裁剪模式'返回分割模式")
        print("7. 完成后点击'完成分割'")
        print("\n建议裁剪的区域:")
        print("• 广告区域 (50, 150) -> (150, 200)")
        print("• 水印 (650, 250) -> (750, 300)")
        print("• 无关内容 (50, 550) -> (150, 600)")
        print("• 页脚信息 (650, 750) -> (750, 800)")
        
        # 创建手动分割对话框
        dialog = ManualSplitDialog(root, test_img, "裁剪测试")
        
        # 等待对话框关闭
        root.wait_window(dialog.dialog)
        
        # 检查结果
        if hasattr(dialog, 'split_result') and dialog.split_result:
            print(f"\n✅ 分割和裁剪成功！")
            print(f"生成片段数: {len(dialog.split_result)}")
            print(f"使用的裁剪区域数: {len(dialog.crop_regions)}")
            
            # 保存结果图片以便查看
            for i, segment in enumerate(dialog.split_result):
                img = segment['image']
                output_path = f"test_result_segment_{i+1}.png"
                img.save(output_path)
                print(f"  片段 {i+1}: {img.width}x{img.height}px -> {output_path}")
            
            if dialog.crop_regions:
                print(f"\n裁剪区域:")
                for i, (x1, y1, x2, y2) in enumerate(dialog.crop_regions):
                    print(f"  区域 {i+1}: ({x1}, {y1}) -> ({x2}, {y2})")
        else:
            print("❌ 分割被取消或失败")
        
        root.destroy()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保gui_video_converter.py在同一目录下")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("区域裁剪功能测试")
    print("=" * 30)
    
    print("功能说明:")
    print("✨ 分割模式:")
    print("  - 单击空白处：添加分割点")
    print("  - 拖拽分割线：移动位置")
    print("  - 双击分割线：删除")
    print()
    print("✨ 裁剪模式:")
    print("  - 拖拽框选：选择要排除的区域")
    print("  - 单击裁剪框：选中区域")
    print("  - 双击裁剪框：删除区域")
    print("  - 支持多个裁剪区域")
    print()
    
    choice = input("是否开始测试？(y/n): ").lower()
    if choice == 'y':
        test_crop_functionality()
    else:
        print("测试取消")

if __name__ == "__main__":
    main()
