# 长图转视频工具 - GUI版本

## 🎯 主要特性

### ✨ 完整的图形界面
- **文件选择**：通过资源管理器选择PDF或图片文件
- **可视化设置**：直观的设置选项和参数配置
- **实时日志**：详细的处理过程显示
- **进度条**：转换进度实时显示

### 🖱️ 手动分割功能
- **可视化分割界面**：直接在图片上点击添加分割点
- **拖拽移动**：点击选中分割线（变蓝色），拖拽移动位置
- **双击删除**：双击分割线删除分割点
- **自动建议**：智能建议分割点位置
- **实时反馈**：显示鼠标位置和分割点信息

### 🎤 多语音选择
- **20种Edge-TTS中文语音**：包含男声女声多种选择
- **语音测试**：可以预先测试语音效果
- **本地语音支持**：支持系统本地语音引擎
- **无语音模式**：纯图片视频模式

### 🖼️ 智能图片处理
- **智能适配**：自动保持比例，避免畸变
- **多种处理方式**：裁剪、拉伸、等比缩放等
- **实时预览**：处理效果即时可见

## 🚀 使用方法

### 1. 启动程序
- 双击 `启动GUI.bat` 或
- 运行 `python gui_video_converter.py`

### 2. 选择文件
- 点击"浏览文件"按钮
- 在资源管理器中选择PDF或图片文件
- 支持格式：PDF, JPG, PNG, BMP, TIFF, WEBP

### 3. 配置设置
- **分割方式**：
  - PDF文件：自动按页面分割（无需选择）
  - 图片文件：推荐选择"手动分割（可视化）"
- **图片处理**：推荐选择"智能适配"
- **语音引擎**：选择Edge-TTS获得最佳效果
- **输出目录**：设置视频保存位置

### 4. 预览分割
- 点击"预览分割"按钮
- **PDF文件**：自动按页面提取，无需分割操作
- **图片文件**：
  - 手动分割：打开可视化分割界面
  - 自动分割：按选择的方式自动分割
  - 在图片上点击添加分割点
  - 点击分割线选中（变蓝色）
  - 拖拽分割线移动位置
  - 双击分割线删除分割点

### 5. 编辑文案
- 点击"编辑文案"按钮
- 在文案编辑器中修改每段的讲解内容
- 支持批量修改、导入导出等功能

### 6. 选择语音
- 点击"选择语音"按钮
- 从20种中文语音中选择合适的
- 可以测试语音效果

### 7. 开始转换
- 点击"开始转换"按钮
- 观察进度条和日志信息
- 转换完成后可直接打开文件或文件夹

## 📄 文件类型处理说明

### PDF文件处理
- **自动分割**：按PDF页面自动分割，每页生成一个视频片段
- **无需手动分割**：界面会自动隐藏分割方式选项
- **页面提取**：自动提取每页内容作为图片
- **文案生成**：为每页自动生成基础文案
- **适用场景**：演示文档、报告、教材等

### 图片文件处理
- **需要分割**：长图需要分割成多个片段
- **分割方式选择**：
  - **手动分割（推荐）**：可视化界面精确控制分割点
  - **自动分割**：按16:9比例自动分割
  - **固定高度分割**：按固定像素高度分割
  - **指定分割数量**：指定分割成几段
  - **重叠滚动分割**：带重叠的滚动效果
- **适用场景**：长截图、信息图、流程图等

### 处理流程对比

| 文件类型 | 分割选项 | 预览步骤 | 文案编辑 | 转换过程 |
|---------|---------|---------|---------|---------|
| PDF | 无（自动按页） | 直接预览页面 | 编辑每页文案 | 按页面转换 |
| 图片 | 多种选择 | 分割预览 | 编辑每段文案 | 按片段转换 |

## 🎭 语音选择指南

### 女声推荐
- **晓晓**：温柔甜美，适合大多数内容 ⭐推荐
- **晓伊**：清新自然，适合教育内容
- **晓辰**：知性优雅，适合商务内容
- **晓睿**：聪慧理性，适合专业内容

### 男声推荐
- **云希**：成熟稳重，适合正式内容 ⭐推荐
- **云扬**：专业播音，适合新闻类内容 ⭐推荐
- **云健**：活力阳光，适合活泼内容
- **云枫**：磁性深沉，适合深度内容

## 🔧 故障排除

### 语音问题
- 如果Edge-TTS无法使用，选择"本地语音"
- 网络问题可能导致某些语音不可用
- 程序会自动使用备用语音方案

### 文件问题
- 确保文件格式正确且未损坏
- 大文件可能需要更长处理时间
- 复杂图片建议先预览分割

### 性能问题
- 关闭其他占用内存的程序
- 大文件建议分批处理
- 确保有足够的磁盘空间

## 📁 输出文件

程序会在指定的输出目录生成：
- `[文件名]_video.mp4`：最终视频文件
- 临时文件会自动清理

## 🆕 GUI版本优势

### 相比命令行版本
- ✅ 更直观的操作界面
- ✅ 实时的处理反馈
- ✅ 可视化的分割操作
- ✅ 更好的错误处理
- ✅ 进度显示和状态管理

### 适用场景
- 🎯 不熟悉命令行的用户
- 🎯 需要精确控制分割点的场景
- 🎯 需要频繁调整参数的工作流
- 🎯 需要批量处理多个文件

## 💡 使用技巧

1. **文件选择**：使用资源管理器可以更方便地浏览和选择文件
2. **分割预览**：建议先预览分割效果再进行转换
3. **文案编辑**：可以导入现有文案文件，提高效率
4. **语音测试**：转换前先测试语音效果，确保满意
5. **批量处理**：可以重复使用相同设置处理多个文件

## 🔄 版本更新

- **v1.0**：基础GUI功能
- **v1.1**：添加手动分割界面
- **v1.2**：增强语音选择和测试功能
- **v1.3**：优化图片处理和错误处理

---

**技术支持**：如遇问题请查看日志信息或联系开发者
**更新日期**：2024年最新版
