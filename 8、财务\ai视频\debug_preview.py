#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试预览分割功能
"""

import sys
import os
from pathlib import Path
from PIL import Image

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_image_loading():
    """测试图片加载"""
    print("测试图片加载功能...")
    
    # 查找测试图片
    test_files = [
        "财经指标名称解析第一期：毛利率.png",
        "test_long_image.png",
        "test_square.png"
    ]
    
    test_file = None
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("❌ 没有找到测试图片文件")
        return None
    
    try:
        print(f"📁 加载图片: {test_file}")
        img = Image.open(test_file)
        print(f"✅ 图片加载成功: {img.width}x{img.height}px, 模式: {img.mode}")
        return img, test_file
    except Exception as e:
        print(f"❌ 图片加载失败: {e}")
        return None

def test_converter_import():
    """测试转换器导入"""
    print("测试转换器导入...")
    
    try:
        from long_image_to_video import EnhancedImageVideoConverter, check_dependencies
        print("✅ 转换器模块导入成功")
        
        deps = check_dependencies()
        if deps:
            print("✅ 依赖检查通过")
            
            converter = EnhancedImageVideoConverter(deps)
            print("✅ 转换器创建成功")
            return converter
        else:
            print("❌ 依赖检查失败")
            return None
            
    except Exception as e:
        print(f"❌ 转换器导入失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_extract_content(converter, test_file):
    """测试内容提取"""
    print(f"测试内容提取: {test_file}")
    
    try:
        pages_data = converter.extract_content(test_file)
        if pages_data:
            print(f"✅ 内容提取成功，共 {len(pages_data)} 个片段")
            for i, page in enumerate(pages_data):
                img = page.get('image')
                if img:
                    print(f"  片段 {i+1}: {img.width}x{img.height}px")
                else:
                    print(f"  片段 {i+1}: 无图片数据")
            return pages_data
        else:
            print("❌ 内容提取失败，返回空数据")
            return None
            
    except Exception as e:
        print(f"❌ 内容提取异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_manual_split_dialog():
    """测试手动分割对话框"""
    print("测试手动分割对话框...")
    
    # 加载图片
    result = test_image_loading()
    if not result:
        return
    
    img, test_file = result
    
    try:
        import tkinter as tk
        from gui_video_converter import ManualSplitDialog
        
        print("创建主窗口...")
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("创建手动分割对话框...")
        dialog = ManualSplitDialog(root, img, Path(test_file).stem)
        
        print("等待对话框...")
        root.wait_window(dialog.dialog)
        
        if hasattr(dialog, 'split_result') and dialog.split_result:
            print(f"✅ 分割成功，共 {len(dialog.split_result)} 个片段")
        else:
            print("⚠️ 分割被取消或失败")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ 手动分割对话框测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("调试预览分割功能")
    print("=" * 30)
    
    # 测试1: 图片加载
    result = test_image_loading()
    if not result:
        return
    
    img, test_file = result
    
    # 测试2: 转换器导入
    converter = test_converter_import()
    if not converter:
        return
    
    # 测试3: 内容提取
    pages_data = test_extract_content(converter, test_file)
    
    # 测试4: 手动分割对话框
    print("\n" + "=" * 30)
    choice = input("是否测试手动分割对话框？(y/n): ").lower()
    if choice == 'y':
        test_manual_split_dialog()
    
    print("\n调试完成！")

if __name__ == "__main__":
    main()
