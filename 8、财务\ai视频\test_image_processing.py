#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片处理功能 - 避免畸变
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_images_with_different_ratios():
    """创建不同宽高比的测试图片"""
    
    # 创建不同宽高比的测试图片
    test_images = []
    
    # 1. 正方形图片 (1:1)
    img1 = Image.new('RGB', (800, 800), 'lightblue')
    draw1 = ImageDraw.Draw(img1)
    draw1.rectangle([50, 50, 750, 750], outline='blue', width=5)
    draw1.text((300, 380), "正方形 1:1", fill='darkblue', font=get_font(40))
    draw1.text((250, 420), "800x800 像素", fill='darkblue', font=get_font(30))
    img1.save("test_square.png")
    test_images.append(("test_square.png", "正方形 (1:1)"))
    
    # 2. 宽图片 (16:9)
    img2 = Image.new('RGB', (1600, 900), 'lightgreen')
    draw2 = ImageDraw.Draw(img2)
    draw2.rectangle([50, 50, 1550, 850], outline='green', width=5)
    draw2.text((600, 430), "宽屏 16:9", fill='darkgreen', font=get_font(50))
    draw2.text((550, 480), "1600x900 像素", fill='darkgreen', font=get_font(40))
    img2.save("test_wide.png")
    test_images.append(("test_wide.png", "宽屏 (16:9)"))
    
    # 3. 高图片 (9:16)
    img3 = Image.new('RGB', (900, 1600), 'lightyellow')
    draw3 = ImageDraw.Draw(img3)
    draw3.rectangle([50, 50, 850, 1550], outline='orange', width=5)
    draw3.text((300, 780), "竖屏 9:16", fill='darkorange', font=get_font(40))
    draw3.text((250, 820), "900x1600", fill='darkorange', font=get_font(30))
    draw3.text((280, 850), "像素", fill='darkorange', font=get_font(30))
    img3.save("test_tall.png")
    test_images.append(("test_tall.png", "竖屏 (9:16)"))
    
    # 4. 超宽图片 (21:9)
    img4 = Image.new('RGB', (2100, 900), 'lightpink')
    draw4 = ImageDraw.Draw(img4)
    draw4.rectangle([50, 50, 2050, 850], outline='purple', width=5)
    draw4.text((800, 430), "超宽屏 21:9", fill='purple', font=get_font(50))
    draw4.text((750, 480), "2100x900 像素", fill='purple', font=get_font(40))
    img4.save("test_ultrawide.png")
    test_images.append(("test_ultrawide.png", "超宽屏 (21:9)"))
    
    # 5. 超高图片 (长图模拟)
    img5 = Image.new('RGB', (800, 3200), 'lightcyan')
    draw5 = ImageDraw.Draw(img5)
    draw5.rectangle([50, 50, 750, 3150], outline='teal', width=5)
    
    # 在长图中添加多个区域标识
    for i in range(4):
        y_pos = 200 + i * 800
        draw5.rectangle([100, y_pos, 700, y_pos + 600], outline='darkblue', width=3)
        draw5.text((250, y_pos + 280), f"区域 {i+1}", fill='teal', font=get_font(40))
        draw5.text((200, y_pos + 320), f"Y: {y_pos}-{y_pos+600}", fill='teal', font=get_font(25))
    
    draw5.text((200, 100), "长图 1:4", fill='teal', font=get_font(50))
    draw5.text((150, 150), "800x3200 像素", fill='teal', font=get_font(35))
    img5.save("test_long.png")
    test_images.append(("test_long.png", "长图 (1:4)"))
    
    return test_images

def get_font(size):
    """获取字体"""
    try:
        return ImageFont.truetype("arial.ttf", size)
    except:
        return ImageFont.load_default()

def create_combined_test_image():
    """创建一个包含不同高度片段的组合长图"""
    
    # 创建不同高度的片段
    segments = []
    colors = ['lightblue', 'lightgreen', 'lightyellow', 'lightpink', 'lightcyan']
    heights = [400, 600, 300, 800, 500]  # 不同的高度
    
    total_height = sum(heights)
    width = 800
    
    # 创建组合图片
    combined_img = Image.new('RGB', (width, total_height), 'white')
    
    y_offset = 0
    for i, (color, height) in enumerate(zip(colors, heights)):
        # 创建片段
        segment = Image.new('RGB', (width, height), color)
        draw = ImageDraw.Draw(segment)
        
        # 添加边框
        draw.rectangle([10, 10, width-10, height-10], outline='black', width=3)
        
        # 添加文字
        text = f"片段 {i+1}"
        draw.text((width//2 - 60, height//2 - 20), text, fill='black', font=get_font(30))
        
        size_text = f"{width}x{height}px"
        draw.text((width//2 - 80, height//2 + 20), size_text, fill='darkblue', font=get_font(20))
        
        # 添加Y坐标信息
        coord_text = f"Y: {y_offset}-{y_offset + height}"
        draw.text((20, 30), coord_text, fill='darkred', font=get_font(18))
        
        # 粘贴到组合图片
        combined_img.paste(segment, (0, y_offset))
        y_offset += height
    
    # 保存组合图片
    combined_img.save("test_combined_segments.png")
    
    print(f"✅ 创建组合长图: test_combined_segments.png")
    print(f"📏 总尺寸: {width} x {total_height}")
    print("📊 片段信息:")
    y_pos = 0
    for i, height in enumerate(heights):
        print(f"   片段 {i+1}: {width}x{height}px, Y坐标 {y_pos}-{y_pos + height}")
        y_pos += height
    
    return "test_combined_segments.png"

def main():
    print("创建测试图片...")
    
    # 创建不同宽高比的测试图片
    test_images = create_test_images_with_different_ratios()
    
    print("✅ 创建的测试图片:")
    for filename, description in test_images:
        print(f"   {filename} - {description}")
    
    # 创建组合长图
    print(f"\n创建组合长图...")
    combined_image = create_combined_test_image()
    
    print(f"\n🎯 测试建议:")
    print("1. 使用不同的图片测试各种处理方式:")
    print("   python long_image_to_video.py test_square.png")
    print("   python long_image_to_video.py test_wide.png")
    print("   python long_image_to_video.py test_tall.png")
    print("   python long_image_to_video.py test_long.png")
    print("   python long_image_to_video.py test_combined_segments.png")
    
    print(f"\n2. 在创建视频时选择不同的图片处理方式:")
    print("   1. 智能适配（推荐）- 保持比例，添加背景")
    print("   2. 居中裁剪 - 裁剪到16:9比例")
    print("   3. 拉伸填充 - 强制拉伸到16:9（可能畸变）")
    print("   4. 等比缩放 - 保持原比例，上下留黑边")
    
    print(f"\n3. 对于长图，建议使用手动分割功能:")
    print("   选择 '5. 手动确定分割点（可视化）'")

if __name__ == "__main__":
    main()
