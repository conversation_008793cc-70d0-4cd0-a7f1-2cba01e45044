#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长图转视频工具 - GUI版本
支持PDF和长图转视频，完整的图形界面操作
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import threading
from pathlib import Path
import tempfile
import shutil
import time
import json
import math

# 导入原有的转换器类
try:
    from long_image_to_video import EnhancedImageVideoConverter, check_dependencies
except ImportError:
    messagebox.showerror("错误", "无法导入转换器模块，请确保 long_image_to_video.py 在同一目录下")
    sys.exit(1)

class VideoConverterGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("长图转视频工具 - GUI版")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置图标和样式
        self.setup_styles()
        
        # 初始化变量
        self.selected_file = None
        self.converter = None
        self.pages_data = []
        self.is_processing = False
        
        # 检查依赖
        self.deps = check_dependencies()
        if not self.deps:
            messagebox.showerror("错误", "依赖检查失败，请安装必要的依赖包")
            sys.exit(1)
        
        # 创建界面
        self.create_widgets()
        
        # 显示欢迎信息
        self.log_message("🎉 长图转视频工具已启动")
        self.log_message("✨ 支持PDF和长图转视频")
        self.log_message("✨ 支持手动分割、多语音选择、智能图片处理")
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        
        # 设置主题
        try:
            style.theme_use('clam')
        except:
            pass
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="长图转视频工具", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        self.create_file_selection_area(main_frame, row=1)
        
        # 设置区域
        self.create_settings_area(main_frame, row=2)
        
        # 控制按钮区域
        self.create_control_area(main_frame, row=3)
        
        # 日志区域
        self.create_log_area(main_frame, row=4)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def create_file_selection_area(self, parent, row):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text="文件选择", padding="10")
        file_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 文件路径显示
        ttk.Label(file_frame, text="选择文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.file_path_var = tk.StringVar(value="未选择文件")
        file_path_label = ttk.Label(file_frame, textvariable=self.file_path_var, 
                                   relief='sunken', padding="5")
        file_path_label.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 浏览按钮
        browse_btn = ttk.Button(file_frame, text="浏览文件", command=self.browse_file)
        browse_btn.grid(row=0, column=2)
        
        # 文件信息显示
        self.file_info_var = tk.StringVar(value="")
        file_info_label = ttk.Label(file_frame, textvariable=self.file_info_var, 
                                   foreground='blue')
        file_info_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
    
    def create_settings_area(self, parent, row):
        """创建设置区域"""
        settings_frame = ttk.LabelFrame(parent, text="转换设置", padding="10")
        settings_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(1, weight=1)
        
        # 分割方式选择（仅对图片文件显示）
        self.split_label = ttk.Label(settings_frame, text="分割方式:")
        self.split_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.split_method_var = tk.StringVar(value="manual")
        split_methods = [
            ("手动分割（可视化）", "manual"),
            ("自动分割（16:9比例）", "auto_ratio"),
            ("固定高度分割", "fixed_height"),
            ("指定分割数量", "by_count"),
            ("重叠滚动分割", "overlap")
        ]

        self.split_combo = ttk.Combobox(settings_frame, textvariable=self.split_method_var,
                                       values=[method[0] for method in split_methods], state='readonly')
        self.split_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.split_combo.set("手动分割（可视化）")

        # 存储分割方式数据
        self.split_methods_data = split_methods
        
        # 图片处理方式
        ttk.Label(settings_frame, text="图片处理:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        
        self.image_process_var = tk.StringVar(value="smart")
        image_methods = [
            ("智能适配（推荐）", "smart"),
            ("居中裁剪", "crop"),
            ("拉伸填充", "stretch"),
            ("等比缩放", "letterbox")
        ]
        
        image_combo = ttk.Combobox(settings_frame, textvariable=self.image_process_var,
                                  values=[method[0] for method in image_methods], state='readonly')
        image_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        image_combo.set("智能适配（推荐）")
        
        # 语音设置
        ttk.Label(settings_frame, text="语音引擎:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        
        self.voice_engine_var = tk.StringVar(value="edge")
        voice_engines = [
            ("Edge-TTS（高质量）", "edge"),
            ("本地语音（pyttsx3）", "pyttsx3"),
            ("无语音", "none")
        ]
        
        voice_combo = ttk.Combobox(settings_frame, textvariable=self.voice_engine_var,
                                  values=[engine[0] for engine in voice_engines], state='readonly')
        voice_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        voice_combo.set("Edge-TTS（高质量）")
        
        # 语音选择按钮
        voice_select_btn = ttk.Button(settings_frame, text="选择语音", 
                                     command=self.select_voice)
        voice_select_btn.grid(row=2, column=2)
        
        # 输出设置
        ttk.Label(settings_frame, text="输出目录:").grid(row=3, column=0, sticky=tk.W, padx=(0, 10))
        
        self.output_dir_var = tk.StringVar(value="output")
        output_entry = ttk.Entry(settings_frame, textvariable=self.output_dir_var)
        output_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        output_browse_btn = ttk.Button(settings_frame, text="浏览", 
                                      command=self.browse_output_dir)
        output_browse_btn.grid(row=3, column=2)
    
    def create_control_area(self, parent, row):
        """创建控制按钮区域"""
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=row, column=0, columnspan=3, pady=(0, 10))
        
        # 预览按钮
        self.preview_btn = ttk.Button(control_frame, text="预览分割", 
                                     command=self.preview_split, state='disabled')
        self.preview_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 编辑文案按钮
        self.edit_script_btn = ttk.Button(control_frame, text="编辑文案", 
                                         command=self.edit_scripts, state='disabled')
        self.edit_script_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 开始转换按钮
        self.convert_btn = ttk.Button(control_frame, text="开始转换", 
                                     command=self.start_conversion, state='disabled')
        self.convert_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按钮
        self.stop_btn = ttk.Button(control_frame, text="停止", 
                                  command=self.stop_conversion, state='disabled')
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开输出目录按钮
        self.open_output_btn = ttk.Button(control_frame, text="打开输出目录", 
                                         command=self.open_output_directory)
        self.open_output_btn.pack(side=tk.LEFT)
    
    def create_log_area(self, parent, row):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清除日志按钮
        clear_log_btn = ttk.Button(log_frame, text="清除日志", command=self.clear_log)
        clear_log_btn.grid(row=1, column=0, sticky=tk.E, pady=(5, 0))

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)

    def browse_file(self):
        """浏览选择文件"""
        filetypes = [
            ("所有支持的文件", "*.pdf;*.jpg;*.jpeg;*.png;*.bmp;*.tiff;*.tif;*.webp"),
            ("PDF文件", "*.pdf"),
            ("图片文件", "*.jpg;*.jpeg;*.png;*.bmp;*.tiff;*.tif;*.webp"),
            ("所有文件", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="选择PDF或图片文件",
            filetypes=filetypes,
            initialdir=os.getcwd()
        )

        if filename:
            self.selected_file = filename
            self.file_path_var.set(filename)

            # 显示文件信息
            file_path = Path(filename)
            file_size = file_path.stat().st_size / 1024 / 1024  # MB
            file_info = f"文件: {file_path.name} | 大小: {file_size:.1f} MB | 类型: {file_path.suffix.upper()}"
            self.file_info_var.set(file_info)

            # 根据文件类型显示或隐藏分割选项
            is_pdf = file_path.suffix.lower() == '.pdf'
            if is_pdf:
                # PDF文件不需要分割选项
                self.split_label.grid_remove()
                self.split_combo.grid_remove()
                self.log_message("📄 PDF文件将按页面自动分割")
            else:
                # 图片文件需要分割选项
                self.split_label.grid()
                self.split_combo.grid()

            # 启用预览按钮
            self.preview_btn.config(state='normal')
            self.convert_btn.config(state='normal')

            self.log_message(f"✅ 已选择文件: {file_path.name}")

    def browse_output_dir(self):
        """浏览选择输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir_var.get()
        )

        if directory:
            self.output_dir_var.set(directory)
            self.log_message(f"✅ 输出目录设置为: {directory}")

    def select_voice(self):
        """选择语音"""
        engine_text = self.voice_engine_var.get()
        if "Edge-TTS" in engine_text:
            self.show_voice_selection_dialog()
        elif "本地语音" in engine_text:
            messagebox.showinfo("语音设置", "本地语音将使用系统默认中文语音")
        else:
            messagebox.showinfo("语音设置", "已设置为无语音模式")

    def show_voice_selection_dialog(self):
        """显示语音选择对话框"""
        voice_dialog = VoiceSelectionDialog(self.root)
        self.root.wait_window(voice_dialog.dialog)

        if hasattr(voice_dialog, 'selected_voice') and voice_dialog.selected_voice:
            self.selected_voice = voice_dialog.selected_voice
            self.log_message(f"✅ 已选择语音: {voice_dialog.selected_voice_name}")

    def preview_split(self):
        """预览分割"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择文件")
            return

        self.log_message("🔍 开始预览分割...")

        # 在新线程中处理
        threading.Thread(target=self._preview_split_thread, daemon=True).start()

    def _preview_split_thread(self):
        """预览分割的线程函数"""
        try:
            # 创建转换器
            if not self.converter:
                self.converter = EnhancedImageVideoConverter(self.deps)

            # 检查文件类型
            file_path = Path(self.selected_file)
            is_pdf = file_path.suffix.lower() == '.pdf'

            self.root.after(0, lambda: self.log_message("📖 正在提取文件内容..."))

            if is_pdf:
                # PDF文件处理
                self.root.after(0, lambda: self.log_message("📄 处理PDF文件..."))
                self.pages_data = self.converter.extract_content(self.selected_file)
            else:
                # 图片文件处理
                self.root.after(0, lambda: self.log_message("🖼️ 处理图片文件..."))

                # 直接加载图片
                try:
                    from PIL import Image
                    img = Image.open(self.selected_file)

                    # 创建单个页面数据
                    self.pages_data = [{
                        'page_num': 1,
                        'image': img,
                        'original_text': f"{file_path.stem} - 原始图片",
                        'script_text': f"现在我们来看{file_path.stem}的内容。",
                        'use_audio': True
                    }]

                    self.root.after(0, lambda: self.log_message(f"✅ 图片加载成功: {img.width}x{img.height}px"))

                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"❌ 图片加载失败: {e}"))
                    print(f"图片加载错误: {e}")
                    return

            if not self.pages_data:
                self.root.after(0, lambda: self.log_message("❌ 文件内容提取失败"))
                return

            self.root.after(0, lambda: self.log_message(f"✅ 成功提取 {len(self.pages_data)} 个片段"))

            if is_pdf:
                # PDF文件直接完成预览
                self.root.after(0, lambda: self.log_message("✅ PDF预览完成，可以编辑文案和开始转换"))
                self.root.after(0, lambda: self.edit_script_btn.config(state='normal'))
            else:
                # 图片文件根据分割方式处理
                split_method = self.split_method_var.get()
                self.root.after(0, lambda: self.log_message(f"📐 分割方式: {split_method}"))

                if "手动分割" in split_method:
                    self.root.after(0, self.show_manual_split_dialog)
                else:
                    # 其他自动分割方式
                    self.root.after(0, self._apply_auto_split_method)

        except Exception as e:
            error_msg = f"❌ 预览失败: {e}"
            self.root.after(0, lambda: self.log_message(error_msg))
            import traceback
            print(f"预览线程错误: {e}")
            print(traceback.format_exc())

    def _apply_auto_split_method(self):
        """应用自动分割方式"""
        try:
            split_method = self.split_method_var.get()

            if not self.pages_data or not self.pages_data[0].get('image'):
                self.log_message("❌ 没有可分割的图片数据")
                return

            original_img = self.pages_data[0]['image']
            base_name = Path(self.selected_file).stem

            self.log_message(f"🔄 应用分割方式: {split_method}")

            # 根据选择的方式分割
            if "自动分割" in split_method:
                self.pages_data = self.converter.split_by_aspect_ratio(original_img, base_name)
            elif "固定高度" in split_method:
                # 使用默认高度800px
                self.pages_data = self._split_by_fixed_height_auto(original_img, base_name, 800)
            elif "指定分割数量" in split_method:
                # 使用默认5段
                self.pages_data = self._split_by_count_auto(original_img, base_name, 5)
            elif "重叠滚动" in split_method:
                # 使用默认参数
                self.pages_data = self._split_with_overlap_auto(original_img, base_name, 800, 0.2)

            self.log_message(f"✅ 自动分割完成，共 {len(self.pages_data)} 个片段")
            self.edit_script_btn.config(state='normal')

        except Exception as e:
            self.log_message(f"❌ 自动分割失败: {e}")

    def _split_by_fixed_height_auto(self, img, base_name, segment_height):
        """固定高度自动分割"""
        width, height = img.size
        segments_count = math.ceil(height / segment_height)

        pages_data = []
        for i in range(segments_count):
            y_start = i * segment_height
            y_end = min((i + 1) * segment_height, height)

            cropped = img.crop((0, y_start, width, y_end))
            pages_data.append({
                'page_num': i + 1,
                'image': cropped,
                'original_text': f"{base_name} - 第{i + 1}段",
                'script_text': f"现在我们来看第{i + 1}段的内容。",
                'use_audio': True
            })

        return pages_data

    def _split_by_count_auto(self, img, base_name, segments_count):
        """按数量自动分割"""
        width, height = img.size
        segment_height = height // segments_count

        pages_data = []
        for i in range(segments_count):
            y_start = i * segment_height
            y_end = (i + 1) * segment_height if i < segments_count - 1 else height

            cropped = img.crop((0, y_start, width, y_end))
            pages_data.append({
                'page_num': i + 1,
                'image': cropped,
                'original_text': f"{base_name} - 第{i + 1}段",
                'script_text': f"接下来我们来看第{i + 1}段的详细内容。",
                'use_audio': True
            })

        return pages_data

    def _split_with_overlap_auto(self, img, base_name, segment_height, overlap_ratio):
        """重叠滚动自动分割"""
        width, height = img.size
        overlap_pixels = int(segment_height * overlap_ratio)
        step = segment_height - overlap_pixels
        segments_count = math.ceil((height - overlap_pixels) / step)

        pages_data = []
        for i in range(segments_count):
            y_start = i * step
            y_end = min(y_start + segment_height, height)

            cropped = img.crop((0, y_start, width, y_end))
            pages_data.append({
                'page_num': i + 1,
                'image': cropped,
                'original_text': f"{base_name} - 第{i + 1}段（滚动）",
                'script_text': f"现在我们滚动到第{i + 1}部分，请注意观察。",
                'use_audio': True
            })

        return pages_data

    def show_manual_split_dialog(self):
        """显示手动分割对话框"""
        print("开始显示手动分割对话框...")

        if not self.pages_data:
            messagebox.showwarning("警告", "没有可分割的内容")
            return

        # 检查是否为PDF文件
        file_path = Path(self.selected_file)
        if file_path.suffix.lower() == '.pdf':
            messagebox.showinfo("提示", "PDF文件已按页面自动分割，无需手动分割")
            self.edit_script_btn.config(state='normal')
            return

        # 获取原始图片
        original_img = self.pages_data[0]['image'] if self.pages_data else None
        if not original_img:
            messagebox.showerror("错误", "无法获取图片数据")
            print("错误: 无法获取图片数据")
            return

        print(f"准备显示分割对话框，图片尺寸: {original_img.width}x{original_img.height}")
        self.log_message("🖼️ 打开手动分割界面...")

        try:
            # 显示手动分割界面
            split_dialog = ManualSplitDialog(self.root, original_img,
                                            Path(self.selected_file).stem)
            print("手动分割对话框已创建")

            self.root.wait_window(split_dialog.dialog)
            print("手动分割对话框已关闭")

            if hasattr(split_dialog, 'split_result') and split_dialog.split_result:
                self.pages_data = split_dialog.split_result
                self.log_message(f"✅ 手动分割完成，共 {len(self.pages_data)} 个片段")
                self.edit_script_btn.config(state='normal')
            else:
                self.log_message("⚠️ 手动分割被取消")

        except Exception as e:
            error_msg = f"❌ 手动分割对话框错误: {e}"
            self.log_message(error_msg)
            print(f"手动分割对话框错误: {e}")
            import traceback
            traceback.print_exc()

    def edit_scripts(self):
        """编辑文案"""
        if not self.pages_data:
            messagebox.showwarning("警告", "请先预览分割")
            return

        # 显示文案编辑对话框
        script_dialog = ScriptEditDialog(self.root, self.pages_data)
        self.root.wait_window(script_dialog.dialog)

        if hasattr(script_dialog, 'updated_pages_data'):
            self.pages_data = script_dialog.updated_pages_data
            self.log_message("✅ 文案编辑完成")

    def start_conversion(self):
        """开始转换"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择文件")
            return

        if not self.pages_data:
            messagebox.showwarning("警告", "请先预览分割")
            return

        # 确认开始转换
        result = messagebox.askyesno("确认",
                                   f"确认开始转换？\n"
                                   f"文件: {Path(self.selected_file).name}\n"
                                   f"片段数: {len(self.pages_data)}\n"
                                   f"语音引擎: {self.voice_engine_var.get()}")

        if not result:
            return

        # 设置界面状态
        self.is_processing = True
        self.convert_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress_var.set(0)

        # 在新线程中处理转换
        threading.Thread(target=self._conversion_thread, daemon=True).start()

    def _conversion_thread(self):
        """转换处理线程"""
        try:
            self.log_message("🎬 开始视频转换...")

            # 设置输出路径
            output_dir = Path(self.output_dir_var.get())
            output_dir.mkdir(exist_ok=True)

            file_name = Path(self.selected_file).stem
            output_path = output_dir / f"{file_name}_video.mp4"

            # 创建转换器并设置参数
            if not self.converter:
                self.converter = EnhancedImageVideoConverter(self.deps)

            # 设置语音引擎
            voice_engine = self.voice_engine_var.get()
            if "Edge-TTS" in voice_engine:
                self.converter.voice_engine = 'edge'
                if hasattr(self, 'selected_voice'):
                    self.converter.selected_voice = self.selected_voice
            elif "本地语音" in voice_engine:
                self.converter.voice_engine = 'pyttsx3'
            else:
                self.converter.voice_engine = None

            # 开始转换
            success = self._create_video_with_progress(output_path)

            if success:
                self.log_message(f"🎉 转换成功！")
                self.log_message(f"输出文件: {output_path}")

                # 询问是否打开文件
                self.root.after(0, lambda: self._show_completion_dialog(output_path))
            else:
                self.log_message("❌ 转换失败")

        except Exception as e:
            self.log_message(f"❌ 转换过程出错: {e}")
        finally:
            # 恢复界面状态
            self.root.after(0, self._reset_ui_state)

    def _create_video_with_progress(self, output_path):
        """带进度显示的视频创建"""
        try:
            clips = []
            total_segments = len(self.pages_data)

            for i, page_data in enumerate(self.pages_data):
                if not self.is_processing:  # 检查是否被停止
                    return False

                page_num = page_data['page_num']
                self.log_message(f"处理第 {page_num} 段...")

                # 更新进度
                progress = (i / total_segments) * 90  # 90%用于处理片段
                self.root.after(0, lambda p=progress: self.progress_var.set(p))

                # 处理图片
                img = page_data['image']

                # 根据选择的处理方式处理图片
                image_method = self.image_process_var.get()
                if "居中裁剪" in image_method:
                    img_processed = self.converter.crop_to_aspect_ratio(img, 16/9)
                elif "拉伸填充" in image_method:
                    img_processed = self.converter.stretch_to_size(img, (1280, 720))
                elif "等比缩放" in image_method:
                    img_processed = self.converter.scale_with_letterbox(img, (1280, 720))
                else:  # 智能适配
                    img_processed = self.converter.smart_resize(img, (1280, 720))

                # 添加段号标识
                img_processed = self.converter.add_segment_label(img_processed, page_num)

                # 保存图片
                img_path = self.converter.temp_dir / f"segment_{page_num}.png"
                img_processed.save(img_path)

                # 处理音频
                duration = 5
                audio_path = None

                if page_data.get('use_audio', True) and self.converter.voice_engine:
                    audio_path = self.converter.temp_dir / f"audio_{page_num}.wav"
                    script_text = page_data.get('script_text', f"第{page_num}段内容")

                    self.log_message(f"  🎵 生成语音...")

                    if self.converter.generate_speech(script_text, str(audio_path)):
                        try:
                            audio_clip = self.converter.deps['AudioFileClip'](str(audio_path))
                            duration = max(audio_clip.duration, 3)
                            audio_clip.close()
                            self.log_message(f"  ✅ 语音时长: {duration:.1f}秒")
                        except Exception as e:
                            self.log_message(f"  ⚠️ 音频处理失败: {e}")
                            duration = 5
                            audio_path = None
                    else:
                        self.log_message(f"  ⚠️ 语音生成失败，使用静音")
                        audio_path = None
                        duration = 5

                # 创建视频片段
                try:
                    video_clip = self.converter.deps['ImageClip'](str(img_path), duration=duration)

                    if audio_path and Path(audio_path).exists():
                        audio_clip = self.converter.deps['AudioFileClip'](str(audio_path))
                        video_clip = video_clip.set_audio(audio_clip)

                    clips.append(video_clip)
                    self.log_message(f"  ✅ 第 {page_num} 段完成")

                except Exception as e:
                    self.log_message(f"  ❌ 第 {page_num} 段失败: {e}")
                    continue

            if not clips:
                self.log_message("❌ 没有成功创建任何视频片段")
                return False

            # 合并视频
            self.log_message("正在合并视频...")
            self.root.after(0, lambda: self.progress_var.set(95))

            final_video = self.converter.deps['concatenate_videoclips'](clips)

            self.log_message("正在输出视频文件...")
            self.root.after(0, lambda: self.progress_var.set(98))

            final_video.write_videofile(
                str(output_path),
                fps=24,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )

            # 清理
            final_video.close()
            for clip in clips:
                clip.close()

            self.root.after(0, lambda: self.progress_var.set(100))
            return True

        except Exception as e:
            self.log_message(f"❌ 视频创建失败: {e}")
            return False

    def _show_completion_dialog(self, output_path):
        """显示完成对话框"""
        result = messagebox.askyesnocancel("转换完成",
                                         f"视频转换完成！\n\n"
                                         f"文件: {output_path.name}\n"
                                         f"位置: {output_path.parent}\n\n"
                                         f"是否打开文件？\n"
                                         f"（选择'否'打开文件夹）")

        if result is True:  # 打开文件
            os.startfile(str(output_path))
        elif result is False:  # 打开文件夹
            os.startfile(str(output_path.parent))

    def _reset_ui_state(self):
        """重置界面状态"""
        self.is_processing = False
        self.convert_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_var.set(0)

    def stop_conversion(self):
        """停止转换"""
        if messagebox.askyesno("确认", "确认停止转换？"):
            self.is_processing = False
            self.log_message("⏹️ 用户停止转换")
            self._reset_ui_state()

    def open_output_directory(self):
        """打开输出目录"""
        output_dir = self.output_dir_var.get()
        if os.path.exists(output_dir):
            os.startfile(output_dir)
        else:
            messagebox.showwarning("警告", f"输出目录不存在: {output_dir}")


class VoiceSelectionDialog:
    """语音选择对话框"""
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("选择Edge-TTS语音")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.selected_voice = None
        self.selected_voice_name = None

        self.create_widgets()

    def create_widgets(self):
        """创建组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="选择Edge-TTS语音", font=('Arial', 12, 'bold')).pack(pady=(0, 10))

        # 语音列表
        voices = [
            ('zh-CN-XiaoxiaoNeural', '晓晓 (女声) - 温柔甜美 ⭐推荐'),
            ('zh-CN-YunxiNeural', '云希 (男声) - 成熟稳重 ⭐推荐'),
            ('zh-CN-YunyangNeural', '云扬 (男声) - 专业播音 ⭐推荐'),
            ('zh-CN-XiaoyiNeural', '晓伊 (女声) - 清新自然'),
            ('zh-CN-YunjianNeural', '云健 (男声) - 活力阳光'),
            ('zh-CN-XiaochenNeural', '晓辰 (女声) - 知性优雅'),
            ('zh-CN-XiaohanNeural', '晓涵 (女声) - 亲切温暖'),
            ('zh-CN-XiaomengNeural', '晓梦 (女声) - 可爱活泼'),
            ('zh-CN-XiaomoNeural', '晓墨 (女声) - 成熟知性'),
            ('zh-CN-XiaoqiuNeural', '晓秋 (女声) - 温和亲和'),
            ('zh-CN-XiaoruiNeural', '晓睿 (女声) - 聪慧理性'),
            ('zh-CN-XiaoshuangNeural', '晓双 (女声) - 清脆明亮'),
            ('zh-CN-XiaoxuanNeural', '晓萱 (女声) - 优雅大方'),
            ('zh-CN-XiaoyanNeural', '晓颜 (女声) - 甜美可人'),
            ('zh-CN-XiaoyouNeural', '晓悠 (女声) - 悠扬动听'),
            ('zh-CN-XiaozhenNeural', '晓甄 (女声) - 端庄典雅'),
            ('zh-CN-YunfengNeural', '云枫 (男声) - 磁性深沉'),
            ('zh-CN-YunhaoNeural', '云皓 (男声) - 清朗有力'),
            ('zh-CN-YunxiaNeural', '云夏 (男声) - 热情洋溢'),
            ('zh-CN-YunyeNeural', '云野 (男声) - 自然随性')
        ]

        # 创建列表框
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 列表框
        self.voice_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, height=15)
        self.voice_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.voice_listbox.yview)

        # 添加语音到列表
        for voice_id, voice_name in voices:
            self.voice_listbox.insert(tk.END, voice_name)

        # 默认选择第一个
        self.voice_listbox.selection_set(0)

        # 存储语音数据
        self.voices_data = voices

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # 测试按钮
        ttk.Button(button_frame, text="测试语音", command=self.test_voice).pack(side=tk.LEFT, padx=(0, 10))

        # 确定和取消按钮
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="确定", command=self.confirm).pack(side=tk.RIGHT, padx=(0, 10))

    def test_voice(self):
        """测试选中的语音"""
        selection = self.voice_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个语音")
            return

        voice_id, voice_name = self.voices_data[selection[0]]

        # 在新线程中测试语音
        threading.Thread(target=self._test_voice_thread, args=(voice_id, voice_name), daemon=True).start()

    def _test_voice_thread(self, voice_id, voice_name):
        """测试语音的线程函数"""
        try:
            import subprocess
            import tempfile

            test_text = "大家好，这是语音测试。"

            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

            cmd = [
                "edge-tts",
                "--voice", voice_id,
                "--text", test_text,
                "--write-media", temp_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and Path(temp_path).exists():
                file_size = Path(temp_path).stat().st_size
                if file_size > 500:
                    # 播放音频
                    os.startfile(temp_path)
                    self.dialog.after(0, lambda: messagebox.showinfo("测试成功", f"语音测试成功！\n{voice_name}"))
                else:
                    self.dialog.after(0, lambda: messagebox.showerror("测试失败", "生成的音频文件太小"))
            else:
                self.dialog.after(0, lambda: messagebox.showerror("测试失败", "语音生成失败"))

            # 清理临时文件（延迟删除，让播放器有时间打开）
            threading.Timer(5.0, lambda: Path(temp_path).unlink() if Path(temp_path).exists() else None).start()

        except Exception as e:
            self.dialog.after(0, lambda: messagebox.showerror("测试失败", f"测试过程出错: {e}"))

    def confirm(self):
        """确定选择"""
        selection = self.voice_listbox.curselection()
        if selection:
            self.selected_voice, self.selected_voice_name = self.voices_data[selection[0]]
        self.dialog.destroy()

    def cancel(self):
        """取消选择"""
        self.dialog.destroy()


class ManualSplitDialog:
    """手动分割对话框"""
    def __init__(self, parent, image, base_name):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"手动分割 - {base_name}")
        self.dialog.geometry("1400x900")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.image = image
        self.base_name = base_name
        self.split_points = []
        self.split_result = None

        # 拖拽相关变量
        self.selected_line = None
        self.drag_start_y = None
        self.is_dragging = False

        # 排除区域相关变量
        self.exclude_mode = False
        self.exclude_regions = []  # 存储要排除的区域 [(x1, y1, x2, y2), ...]
        self.current_exclude = None  # 当前正在绘制的排除区域
        self.exclude_start_pos = None
        self.selected_exclude = None  # 选中的排除区域

        # 计算缩放比例 - 确保图片能完整显示
        max_width = 1200
        max_height = 700

        width_scale = max_width / image.width if image.width > max_width else 1.0
        height_scale = max_height / image.height if image.height > max_height else 1.0
        self.scale_factor = min(width_scale, height_scale, 1.0)

        self.display_width = int(image.width * self.scale_factor)
        self.display_height = int(image.height * self.scale_factor)

        print(f"原图尺寸: {image.width}x{image.height}")
        print(f"显示尺寸: {self.display_width}x{self.display_height}")
        print(f"缩放比例: {self.scale_factor}")

        self.create_widgets()

    def create_widgets(self):
        """创建组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题和信息
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(title_frame, text=f"手动分割 - {self.base_name}",
                 font=('Arial', 12, 'bold')).pack(side=tk.LEFT)

        ttk.Label(title_frame, text=f"原图: {self.image.width}×{self.image.height}px",
                 foreground='blue').pack(side=tk.RIGHT)

        # 画布框架
        canvas_frame = ttk.LabelFrame(main_frame, text="图片预览（点击添加分割点，双击删除）", padding="5")
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建画布和滚动条
        canvas_container = ttk.Frame(canvas_frame)
        canvas_container.pack(fill=tk.BOTH, expand=True)

        # 计算画布尺寸
        canvas_width = min(self.display_width, 1200)
        canvas_height = min(self.display_height, 650)

        self.canvas = tk.Canvas(canvas_container,
                               width=canvas_width,
                               height=canvas_height,
                               bg='white',
                               highlightthickness=1,
                               highlightbackground='gray')

        # 滚动条
        v_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.HORIZONTAL, command=self.canvas.xview)

        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        self.canvas.configure(scrollregion=(0, 0, self.display_width, self.display_height))

        # 布局滚动条和画布
        self.canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        canvas_container.columnconfigure(0, weight=1)
        canvas_container.rowconfigure(0, weight=1)

        # 显示图片
        try:
            from PIL import ImageTk, Image

            # 使用高质量重采样
            display_img = self.image.resize((self.display_width, self.display_height), Image.Resampling.LANCZOS)
            self.photo = ImageTk.PhotoImage(display_img)
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)

            print(f"图片显示成功: {self.display_width}x{self.display_height}")

        except Exception as e:
            print(f"图片显示失败: {e}")
            # 创建一个错误提示
            self.canvas.create_text(self.display_width//2, self.display_height//2,
                                  text=f"图片显示失败: {e}",
                                  fill='red', font=('Arial', 12))

        # 绑定事件
        self.canvas.bind("<Button-1>", self.on_click)
        self.canvas.bind("<Double-Button-1>", self.on_double_click)
        self.canvas.bind("<Motion>", self.on_motion)
        self.canvas.bind("<B1-Motion>", self.on_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_release)

        # 添加鼠标位置显示
        self.mouse_pos_var = tk.StringVar(value="鼠标位置: -")
        ttk.Label(canvas_frame, textvariable=self.mouse_pos_var,
                 foreground='gray', font=('Arial', 9)).pack(anchor=tk.W)

        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 左侧按钮组
        left_buttons = ttk.Frame(control_frame)
        left_buttons.pack(side=tk.LEFT)

        ttk.Button(left_buttons, text="清除所有分割点", command=self.clear_splits).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="自动建议分割点", command=self.auto_suggest).pack(side=tk.LEFT, padx=(0, 10))

        # 排除区域功能按钮
        self.exclude_mode_btn = ttk.Button(left_buttons, text="排除区域模式", command=self.toggle_exclude_mode)
        self.exclude_mode_btn.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(left_buttons, text="清除排除区域", command=self.clear_excludes).pack(side=tk.LEFT, padx=(0, 10))

        # 右侧按钮组
        right_buttons = ttk.Frame(control_frame)
        right_buttons.pack(side=tk.RIGHT)

        ttk.Button(right_buttons, text="取消", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(right_buttons, text="完成分割", command=self.finish_split).pack(side=tk.RIGHT, padx=(0, 10))

        # 信息显示
        self.info_var = tk.StringVar(value="点击图片添加分割点，双击分割线删除")
        ttk.Label(main_frame, textvariable=self.info_var, foreground='blue').pack()

        # 使用说明
        self.help_text_var = tk.StringVar()
        self.update_help_text()
        help_label = ttk.Label(main_frame, textvariable=self.help_text_var, justify=tk.LEFT, foreground='gray')
        help_label.pack(pady=(5, 0))

    def on_motion(self, event):
        """处理鼠标移动事件"""
        try:
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)
            actual_x = int(canvas_x / self.scale_factor)
            actual_y = int(canvas_y / self.scale_factor)

            if 0 <= actual_x <= self.image.width and 0 <= actual_y <= self.image.height:
                self.mouse_pos_var.set(f"鼠标位置: X={actual_x}, Y={actual_y}")
            else:
                self.mouse_pos_var.set("鼠标位置: 超出图片范围")
        except:
            self.mouse_pos_var.set("鼠标位置: -")

    def on_click(self, event):
        """处理点击事件"""
        try:
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)
            actual_x = int(canvas_x / self.scale_factor)
            actual_y = int(canvas_y / self.scale_factor)

            print(f"点击位置: canvas_x={canvas_x}, canvas_y={canvas_y}, actual_x={actual_x}, actual_y={actual_y}")

            if self.exclude_mode:
                # 排除区域模式：开始创建排除区域或选中现有区域
                self.handle_exclude_click(actual_x, actual_y, canvas_x, canvas_y)
            else:
                # 分割模式：处理分割线
                self.handle_split_click(actual_y, canvas_y)

        except Exception as e:
            print(f"点击事件处理错误: {e}")

    def handle_exclude_click(self, actual_x, actual_y, canvas_x, canvas_y):
        """处理排除区域模式的点击"""
        # 检查是否点击了现有的排除区域
        clicked_exclude = None
        for i, (x1, y1, x2, y2) in enumerate(self.exclude_regions):
            if x1 <= actual_x <= x2 and y1 <= actual_y <= y2:
                clicked_exclude = i
                break

        if clicked_exclude is not None:
            # 选中排除区域
            self.selected_exclude = clicked_exclude
            print(f"选中排除区域: {clicked_exclude}")
            self.redraw_exclude_regions()
        else:
            # 开始创建新的排除区域
            self.selected_exclude = None
            self.exclude_start_pos = (actual_x, actual_y)
            print(f"开始创建排除区域: {actual_x}, {actual_y}")

    def handle_split_click(self, actual_y, canvas_y):
        """处理分割模式的点击"""
        # 检查是否点击了现有的分割线
        clicked_line = None
        min_distance = float('inf')

        for point in self.split_points:
            canvas_point_y = point * self.scale_factor
            distance = abs(canvas_y - canvas_point_y)
            if distance < 15 and distance < min_distance:  # 15像素的容差
                clicked_line = point
                min_distance = distance

        if clicked_line is not None:
            # 选中分割线
            self.selected_line = clicked_line
            self.drag_start_y = canvas_y
            self.is_dragging = False
            print(f"选中分割线: {clicked_line}")
            self.redraw_split_lines()  # 重绘以高亮选中的线
        else:
            # 取消选中
            self.selected_line = None

            # 检查是否在有效范围内添加新分割点
            if 20 < actual_y < self.image.height - 20:
                # 检查是否与现有分割点太近
                too_close = False
                for existing_y in self.split_points:
                    if abs(actual_y - existing_y) < 30:  # 最小间距30像素
                        too_close = True
                        break

                if not too_close:
                    self.split_points.append(actual_y)
                    self.split_points.sort()
                    self.redraw_split_lines()
                    print(f"添加分割点: {actual_y}")
                else:
                    print("分割点太近，忽略")
            else:
                print("点击位置超出有效范围")

    def on_double_click(self, event):
        """处理双击事件"""
        try:
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)
            actual_x = int(canvas_x / self.scale_factor)
            actual_y = int(canvas_y / self.scale_factor)

            if self.exclude_mode:
                # 排除区域模式：删除排除区域
                for i, (x1, y1, x2, y2) in enumerate(self.exclude_regions):
                    if x1 <= actual_x <= x2 and y1 <= actual_y <= y2:
                        self.exclude_regions.pop(i)
                        self.selected_exclude = None
                        self.redraw_exclude_regions()
                        print(f"删除排除区域: {i}")
                        return
                print("未找到要删除的排除区域")

            else:
                # 分割模式：删除分割线
                # 查找最近的分割线
                closest_point = None
                min_distance = float('inf')

                for point in self.split_points:
                    canvas_point_y = point * self.scale_factor
                    distance = abs(canvas_y - canvas_point_y)
                    if distance < 20 and distance < min_distance:  # 增加容差
                        closest_point = point
                        min_distance = distance

                if closest_point is not None:
                    self.split_points.remove(closest_point)
                    self.selected_line = None
                    self.redraw_split_lines()
                    print(f"删除分割点: {closest_point}")
                else:
                    print("未找到要删除的分割点")

        except Exception as e:
            print(f"双击事件处理错误: {e}")

    def on_drag(self, event):
        """处理拖拽事件"""
        try:
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)
            actual_x = int(canvas_x / self.scale_factor)
            actual_y = int(canvas_y / self.scale_factor)

            if self.exclude_mode and self.exclude_start_pos:
                # 排除区域模式：创建排除区域
                start_x, start_y = self.exclude_start_pos

                # 确保坐标顺序正确
                x1, x2 = min(start_x, actual_x), max(start_x, actual_x)
                y1, y2 = min(start_y, actual_y), max(start_y, actual_y)

                # 限制在图片范围内
                x1 = max(0, min(x1, self.image.width))
                x2 = max(0, min(x2, self.image.width))
                y1 = max(0, min(y1, self.image.height))
                y2 = max(0, min(y2, self.image.height))

                # 更新当前排除区域
                self.current_exclude = (x1, y1, x2, y2)
                self.redraw_exclude_regions()

            elif not self.exclude_mode and self.selected_line is not None:
                # 分割模式：拖拽分割线
                # 标记为正在拖拽
                if not self.is_dragging and self.drag_start_y is not None:
                    if abs(canvas_y - self.drag_start_y) > 5:  # 移动超过5像素才开始拖拽
                        self.is_dragging = True
                        print(f"开始拖拽分割线: {self.selected_line}")

                if self.is_dragging:
                    # 限制在图片范围内
                    if 20 < actual_y < self.image.height - 20:
                        # 检查是否与其他分割线冲突
                        conflict = False
                        for point in self.split_points:
                            if point != self.selected_line and abs(actual_y - point) < 30:
                                conflict = True
                                break

                        if not conflict:
                            # 更新分割点位置
                            old_index = self.split_points.index(self.selected_line)
                            self.split_points[old_index] = actual_y
                            self.selected_line = actual_y

                            # 重新排序
                            self.split_points.sort()

                            # 重绘分割线
                            self.redraw_split_lines()

        except Exception as e:
            print(f"拖拽事件处理错误: {e}")

    def on_release(self, event):
        """处理鼠标释放事件"""
        try:
            if self.exclude_mode and self.current_exclude:
                # 完成排除区域创建
                x1, y1, x2, y2 = self.current_exclude

                # 检查区域大小是否有效（至少20x20像素）
                if abs(x2 - x1) >= 20 and abs(y2 - y1) >= 20:
                    self.exclude_regions.append((x1, y1, x2, y2))
                    print(f"创建排除区域: ({x1}, {y1}) -> ({x2}, {y2})")
                else:
                    print("排除区域太小，忽略")

                # 清理临时状态
                self.current_exclude = None
                self.exclude_start_pos = None
                self.redraw_exclude_regions()

            elif not self.exclude_mode:
                # 分割模式的释放处理
                if self.is_dragging and self.selected_line is not None:
                    print(f"拖拽完成，分割线位置: {self.selected_line}")
                    self.is_dragging = False
                    self.drag_start_y = None
                    # 保持选中状态，但取消拖拽
                    self.redraw_split_lines()
                elif self.selected_line is not None:
                    # 如果没有拖拽，只是点击，也保持选中状态
                    pass

        except Exception as e:
            print(f"释放事件处理错误: {e}")

    def redraw_split_lines(self):
        """重绘分割线"""
        try:
            # 清除现有分割线
            self.canvas.delete('split_line')
            self.canvas.delete('split_text')

            # 绘制新的分割线
            for i, point in enumerate(self.split_points):
                canvas_y = point * self.scale_factor

                # 判断是否为选中的线
                is_selected = (self.selected_line == point)

                # 设置颜色和宽度
                if is_selected:
                    line_color = 'blue'
                    line_width = 4
                    text_color = 'blue'
                    bg_color = 'lightblue'
                else:
                    line_color = 'red'
                    line_width = 3
                    text_color = 'red'
                    bg_color = 'white'

                # 绘制分割线
                self.canvas.create_line(0, canvas_y, self.display_width, canvas_y,
                                      fill=line_color, width=line_width, tags='split_line')

                # 添加半透明背景
                text_bg = self.canvas.create_rectangle(5, canvas_y - 18, 120, canvas_y - 2,
                                                     fill=bg_color, outline=line_color, width=1, tags='split_text')

                # 添加标签
                label_text = f"分割点{i+1}: Y={point}"
                if is_selected:
                    label_text += " (选中)"

                self.canvas.create_text(8, canvas_y - 10, text=label_text,
                                      fill=text_color, anchor=tk.NW, tags='split_text',
                                      font=('Arial', 9, 'bold'))

            # 更新信息
            info_text = f"分割点数量: {len(self.split_points)}"
            if self.split_points:
                sorted_points = sorted(self.split_points)
                info_text += f" | 将产生 {len(self.split_points) + 1} 个片段"
                if self.selected_line is not None:
                    info_text += f" | 选中: Y={self.selected_line} (可拖拽移动)"
                else:
                    info_text += f" | 点击分割线选中，拖拽移动"
            else:
                info_text += " | 点击图片添加分割点"

            self.info_var.set(info_text)

            print(f"重绘分割线完成，当前分割点: {self.split_points}")

        except Exception as e:
            print(f"重绘分割线错误: {e}")

    def clear_splits(self):
        """清除所有分割点"""
        self.split_points.clear()
        self.selected_line = None
        self.is_dragging = False
        self.drag_start_y = None
        self.redraw_all()

    def auto_suggest(self):
        """自动建议分割点"""
        try:
            segment_count = tk.simpledialog.askinteger("自动建议", "建议分割成几段？",
                                                     initialvalue=5, minvalue=2, maxvalue=20)
            if segment_count:
                self.clear_splits()
                segment_height = self.image.height // segment_count

                for i in range(1, segment_count):
                    y = i * segment_height
                    self.split_points.append(y)

                self.redraw_split_lines()
        except:
            pass

    def update_help_text(self):
        """更新帮助文本"""
        if self.exclude_mode:
            help_text = ("排除区域模式：\n"
                        "• 拖拽框选：选择要完全删除的区域\n"
                        "• 单击排除框：选中排除区域\n"
                        "• 双击排除框：删除排除区域\n"
                        "• 退出排除模式：继续分割操作")
        else:
            help_text = ("分割模式：\n"
                        "• 单击空白处：添加分割点\n"
                        "• 单击分割线：选中分割线（变蓝色）\n"
                        "• 拖拽分割线：移动分割点位置\n"
                        "• 双击分割线：删除分割点\n"
                        "• 排除区域模式：完全删除不需要的区域")

        self.help_text_var.set(help_text)

    def toggle_exclude_mode(self):
        """切换排除区域模式"""
        self.exclude_mode = not self.exclude_mode

        if self.exclude_mode:
            self.exclude_mode_btn.config(text="退出排除模式")
            print("🚫 进入排除区域模式 - 拖拽框选要完全删除的区域")
        else:
            self.exclude_mode_btn.config(text="排除区域模式")
            self.selected_exclude = None
            self.current_exclude = None
            self.exclude_start_pos = None
            print("📐 退出排除模式 - 返回分割模式")

        self.update_help_text()
        self.redraw_all()
        print(f"排除区域模式: {self.exclude_mode}")

    def clear_excludes(self):
        """清除所有排除区域"""
        if self.exclude_regions:
            self.exclude_regions.clear()
            self.selected_exclude = None
            self.current_exclude = None
            self.redraw_all()
            print("🗑️ 已清除所有排除区域")
        else:
            print("⚠️ 没有排除区域需要清除")

    def redraw_all(self):
        """重绘所有元素"""
        self.redraw_split_lines()
        self.redraw_exclude_regions()

    def redraw_exclude_regions(self):
        """重绘排除区域"""
        try:
            # 清除现有排除区域
            self.canvas.delete('exclude_region')
            self.canvas.delete('exclude_text')

            # 绘制排除区域
            for i, (x1, y1, x2, y2) in enumerate(self.exclude_regions):
                canvas_x1 = x1 * self.scale_factor
                canvas_y1 = y1 * self.scale_factor
                canvas_x2 = x2 * self.scale_factor
                canvas_y2 = y2 * self.scale_factor

                # 判断是否为选中的区域
                is_selected = (self.selected_exclude == i)

                # 设置颜色 - 使用红色系表示"删除"
                if is_selected:
                    outline_color = 'darkred'
                    fill_color = 'red'
                    text_color = 'darkred'
                else:
                    outline_color = 'red'
                    fill_color = 'red'
                    text_color = 'red'

                # 绘制半透明填充，使用斜线纹理表示"删除"
                self.canvas.create_rectangle(canvas_x1, canvas_y1, canvas_x2, canvas_y2,
                                           outline=outline_color, width=3,
                                           fill=fill_color, stipple='gray50',
                                           tags='exclude_region')

                # 添加删除标记（X）
                center_x = (canvas_x1 + canvas_x2) / 2
                center_y = (canvas_y1 + canvas_y2) / 2

                # 绘制大X标记
                x_size = min(20, (canvas_x2 - canvas_x1) / 4, (canvas_y2 - canvas_y1) / 4)
                self.canvas.create_line(center_x - x_size, center_y - x_size,
                                      center_x + x_size, center_y + x_size,
                                      fill='white', width=3, tags='exclude_region')
                self.canvas.create_line(center_x + x_size, center_y - x_size,
                                      center_x - x_size, center_y + x_size,
                                      fill='white', width=3, tags='exclude_region')

                # 添加标签
                label_text = f"删除区域{i+1}"
                if is_selected:
                    label_text += " (选中)"

                # 添加文字背景
                text_width = len(label_text) * 6
                self.canvas.create_rectangle(center_x - text_width//2, center_y + x_size + 5,
                                           center_x + text_width//2, center_y + x_size + 20,
                                           fill='white', outline=outline_color,
                                           tags='exclude_text')

                self.canvas.create_text(center_x, center_y + x_size + 12, text=label_text,
                                      fill=text_color, font=('Arial', 9, 'bold'),
                                      tags='exclude_text')

            # 绘制当前正在创建的排除区域
            if self.current_exclude:
                x1, y1, x2, y2 = self.current_exclude
                canvas_x1 = x1 * self.scale_factor
                canvas_y1 = y1 * self.scale_factor
                canvas_x2 = x2 * self.scale_factor
                canvas_y2 = y2 * self.scale_factor

                self.canvas.create_rectangle(canvas_x1, canvas_y1, canvas_x2, canvas_y2,
                                           outline='red', width=3, dash=(5, 5),
                                           tags='exclude_region')

        except Exception as e:
            print(f"重绘排除区域错误: {e}")

    def finish_split(self):
        """完成分割"""
        if not self.split_points:
            result = messagebox.askyesnocancel("警告",
                                             "没有设置分割点，是否使用整个图片作为一个片段？\n\n"
                                             "是 - 使用整个图片\n"
                                             "否 - 继续编辑\n"
                                             "取消 - 取消操作")
            if result is True:
                # 使用整个图片
                self.split_result = self.create_single_segment_result()
                self.dialog.destroy()
            elif result is False:
                # 继续编辑
                return
            else:
                # 取消
                return
        else:
            # 显示分割预览信息
            segments_info = []
            all_points = [0] + self.split_points + [self.image.height]

            for i in range(len(all_points) - 1):
                y_start = all_points[i]
                y_end = all_points[i + 1]
                height = y_end - y_start
                segments_info.append(f"片段{i+1}: Y={y_start}-{y_end} (高度:{height}px)")

            preview_text = "分割预览:\n" + "\n".join(segments_info)

            result = messagebox.askyesno("确认分割",
                                       f"确认使用 {len(self.split_points)} 个分割点进行分割？\n\n"
                                       f"{preview_text}\n\n"
                                       f"将产生 {len(self.split_points) + 1} 个视频片段")
            if result:
                self.split_result = self.create_split_result()
                print(f"分割完成，生成 {len(self.split_result)} 个片段")
                self.dialog.destroy()

    def create_split_result(self):
        """创建分割结果"""
        pages_data = []

        # 如果有排除区域，需要重新计算有效的分割区域
        if self.exclude_regions:
            effective_segments = self.calculate_effective_segments()
        else:
            # 没有排除区域，使用原始分割点
            all_points = [0] + self.split_points + [self.image.height]
            effective_segments = []
            for i in range(len(all_points) - 1):
                y_start = all_points[i]
                y_end = all_points[i + 1]
                if y_end - y_start >= 50:  # 跳过太小的片段
                    effective_segments.append((0, y_start, self.image.width, y_end))

        # 创建片段
        for i, (x1, y1, x2, y2) in enumerate(effective_segments):
            if y2 - y1 < 50:  # 跳过太小的片段
                continue

            # 裁剪片段
            cropped = self.image.crop((x1, y1, x2, y2))

            pages_data.append({
                'page_num': len(pages_data) + 1,
                'image': cropped,
                'original_text': f"{self.base_name} - 第{len(pages_data) + 1}段 (Y:{y1}-{y2})",
                'script_text': f"现在我们来看第{len(pages_data) + 1}段的内容。",
                'use_audio': True
            })

        return pages_data

    def calculate_effective_segments(self):
        """计算排除区域后的有效片段"""
        try:
            # 获取所有分割点
            all_points = [0] + self.split_points + [self.image.height]
            effective_segments = []

            for i in range(len(all_points) - 1):
                segment_y_start = all_points[i]
                segment_y_end = all_points[i + 1]

                if segment_y_end - segment_y_start < 50:
                    continue

                # 计算当前片段中的有效区域（排除掉排除区域）
                segment_parts = self.split_segment_by_excludes(
                    0, segment_y_start, self.image.width, segment_y_end
                )

                effective_segments.extend(segment_parts)

            print(f"计算有效片段完成，共 {len(effective_segments)} 个片段")
            return effective_segments

        except Exception as e:
            print(f"计算有效片段失败: {e}")
            # 返回原始片段
            all_points = [0] + self.split_points + [self.image.height]
            segments = []
            for i in range(len(all_points) - 1):
                y_start = all_points[i]
                y_end = all_points[i + 1]
                if y_end - y_start >= 50:
                    segments.append((0, y_start, self.image.width, y_end))
            return segments

    def split_segment_by_excludes(self, x1, y1, x2, y2):
        """将片段按排除区域分割成多个有效部分"""
        # 找到与当前片段重叠的排除区域
        overlapping_excludes = []
        for ex1, ey1, ex2, ey2 in self.exclude_regions:
            # 检查是否有重叠
            if not (ex2 <= x1 or ex1 >= x2 or ey2 <= y1 or ey1 >= y2):
                # 计算重叠区域
                overlap_x1 = max(x1, ex1)
                overlap_y1 = max(y1, ey1)
                overlap_x2 = min(x2, ex2)
                overlap_y2 = min(y2, ey2)
                overlapping_excludes.append((overlap_x1, overlap_y1, overlap_x2, overlap_y2))

        if not overlapping_excludes:
            # 没有排除区域，返回原片段
            return [(x1, y1, x2, y2)]

        # 按Y坐标排序排除区域
        overlapping_excludes.sort(key=lambda r: r[1])

        # 生成有效片段
        valid_parts = []
        current_y = y1

        for ex1, ey1, ex2, ey2 in overlapping_excludes:
            # 添加排除区域之前的部分
            if current_y < ey1:
                if ey1 - current_y >= 30:  # 最小高度30像素
                    valid_parts.append((x1, current_y, x2, ey1))

            # 跳过排除区域
            current_y = max(current_y, ey2)

        # 添加最后一个排除区域之后的部分
        if current_y < y2:
            if y2 - current_y >= 30:  # 最小高度30像素
                valid_parts.append((x1, current_y, x2, y2))

        return valid_parts

    def apply_crop_regions(self, image, segment_y_start, segment_y_end):
        """应用裁剪区域到图片片段"""
        try:
            from PIL import Image as PILImage, ImageDraw

            # 创建遮罩
            mask = PILImage.new('L', image.size, 255)  # 白色表示保留
            mask_draw = ImageDraw.Draw(mask)

            # 找到与当前片段重叠的裁剪区域
            for x1, y1, x2, y2 in self.crop_regions:
                # 计算裁剪区域在当前片段中的相对位置
                if y2 > segment_y_start and y1 < segment_y_end:
                    # 有重叠
                    relative_y1 = max(0, y1 - segment_y_start)
                    relative_y2 = min(image.height, y2 - segment_y_start)

                    if relative_y2 > relative_y1:
                        # 在遮罩上绘制黑色区域（表示要排除的区域）
                        mask_draw.rectangle([x1, relative_y1, x2, relative_y2], fill=0)
                        print(f"应用裁剪区域: ({x1}, {relative_y1}) -> ({x2}, {relative_y2})")

            # 创建背景图片（白色或透明）
            background = PILImage.new('RGB', image.size, (255, 255, 255))

            # 使用遮罩合成图片
            result = PILImage.composite(image, background, mask)

            return result

        except Exception as e:
            print(f"应用裁剪区域失败: {e}")
            return image  # 返回原图

    def create_single_segment_result(self):
        """创建单片段结果（整个图片）"""
        pages_data = [{
            'page_num': 1,
            'image': self.image,
            'original_text': f"{self.base_name} - 完整图片",
            'script_text': f"现在我们来看{self.base_name}的完整内容。",
            'use_audio': True
        }]

        print(f"创建单片段结果: {self.image.width}x{self.image.height}")
        return pages_data

    def cancel(self):
        """取消分割"""
        self.dialog.destroy()


class ScriptEditDialog:
    """文案编辑对话框"""
    def __init__(self, parent, pages_data):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("编辑文案")
        self.dialog.geometry("800x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.pages_data = pages_data.copy()
        self.updated_pages_data = None

        self.create_widgets()
        self.load_scripts()

    def create_widgets(self):
        """创建组件"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="编辑视频文案", font=('Arial', 12, 'bold')).pack(pady=(0, 10))

        # 文案编辑区域
        edit_frame = ttk.LabelFrame(main_frame, text="文案内容", padding="5")
        edit_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 文本框
        self.script_text = scrolledtext.ScrolledText(edit_frame, height=20, wrap=tk.WORD)
        self.script_text.pack(fill=tk.BOTH, expand=True)

        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X)

        # 左侧按钮
        left_buttons = ttk.Frame(control_frame)
        left_buttons.pack(side=tk.LEFT)

        ttk.Button(left_buttons, text="重新生成", command=self.regenerate_scripts).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(left_buttons, text="批量修改", command=self.batch_modify).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(left_buttons, text="导入文件", command=self.import_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(left_buttons, text="导出文件", command=self.export_file).pack(side=tk.LEFT)

        # 右侧按钮
        right_buttons = ttk.Frame(control_frame)
        right_buttons.pack(side=tk.RIGHT)

        ttk.Button(right_buttons, text="取消", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(right_buttons, text="保存", command=self.save_scripts).pack(side=tk.RIGHT, padx=(0, 10))

        # 帮助信息
        help_text = ("格式说明：每段一行，以 [段号] 开头\n"
                    "示例：[1] 这是第一段的讲解内容\n"
                    "空行或以#开头的行将被忽略")
        ttk.Label(main_frame, text=help_text, justify=tk.LEFT, foreground='blue').pack(pady=(5, 0))

    def load_scripts(self):
        """加载文案"""
        content = f"# 视频文案\n"
        content += f"# 格式：每段一行，以 [段号] 开头\n"
        content += f"# 示例：[1] 这是第一段的讲解内容\n"
        content += f"# 空行或以#开头的行将被忽略\n\n"

        for page_data in self.pages_data:
            page_num = page_data['page_num']
            script_text = page_data.get('script_text', page_data.get('original_text', f"第{page_num}段内容"))
            content += f"[{page_num}] {script_text}\n\n"

        self.script_text.delete(1.0, tk.END)
        self.script_text.insert(1.0, content)

    def regenerate_scripts(self):
        """重新生成文案"""
        templates = [
            "现在我们来看第{page}部分的内容。",
            "接下来我们来看第{page}段的详细内容。",
            "这是第{page}段，让我为大家详细解读。",
            "让我们继续往下看，这里是第{page}部分。"
        ]

        choice = messagebox.askyesnocancel("重新生成文案",
                                         "选择生成方式：\n"
                                         "是 - 使用模板生成\n"
                                         "否 - 使用原始文本\n"
                                         "取消 - 不生成")

        if choice is None:
            return

        content = f"# 视频文案 - 重新生成\n"
        content += f"# 格式：每段一行，以 [段号] 开头\n\n"

        for i, page_data in enumerate(self.pages_data):
            page_num = page_data['page_num']

            if choice:  # 使用模板
                template = templates[i % len(templates)]
                script_text = template.replace("{page}", str(page_num))
            else:  # 使用原始文本
                script_text = page_data.get('original_text', f"第{page_num}段内容")

            content += f"[{page_num}] {script_text}\n\n"

        self.script_text.delete(1.0, tk.END)
        self.script_text.insert(1.0, content)

    def batch_modify(self):
        """批量修改"""
        modify_dialog = BatchModifyDialog(self.dialog)
        self.dialog.wait_window(modify_dialog.dialog)

        if hasattr(modify_dialog, 'result') and modify_dialog.result:
            operation, text = modify_dialog.result

            # 获取当前文案
            current_content = self.script_text.get(1.0, tk.END)
            lines = current_content.split('\n')

            modified_lines = []
            for line in lines:
                if line.strip() and line.strip().startswith('[') and ']' in line:
                    # 这是文案行
                    bracket_end = line.index(']')
                    prefix = line[:bracket_end + 1]
                    content = line[bracket_end + 1:].strip()

                    if operation == "prefix":
                        content = f"{text} {content}"
                    elif operation == "suffix":
                        content = f"{content} {text}"
                    elif operation == "replace":
                        old_text, new_text = text
                        content = content.replace(old_text, new_text)

                    modified_lines.append(f"{prefix} {content}")
                else:
                    modified_lines.append(line)

            # 更新文本
            self.script_text.delete(1.0, tk.END)
            self.script_text.insert(1.0, '\n'.join(modified_lines))

    def import_file(self):
        """导入文案文件"""
        filename = filedialog.askopenfilename(
            title="导入文案文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()

                self.script_text.delete(1.0, tk.END)
                self.script_text.insert(1.0, content)

                messagebox.showinfo("成功", "文案文件导入成功")
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {e}")

    def export_file(self):
        """导出文案文件"""
        filename = filedialog.asksaveasfilename(
            title="导出文案文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                content = self.script_text.get(1.0, tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)

                messagebox.showinfo("成功", "文案文件导出成功")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def save_scripts(self):
        """保存文案"""
        content = self.script_text.get(1.0, tk.END)
        lines = content.split('\n')

        # 解析文案
        scripts_dict = {}
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            if line.startswith('[') and ']' in line:
                try:
                    end_bracket = line.index(']')
                    page_num = int(line[1:end_bracket])
                    script_text = line[end_bracket + 1:].strip()
                    scripts_dict[page_num] = script_text
                except (ValueError, IndexError):
                    continue

        # 应用到页面数据
        for page_data in self.pages_data:
            page_num = page_data['page_num']
            if page_num in scripts_dict:
                page_data['script_text'] = scripts_dict[page_num]
                page_data['use_audio'] = True
            else:
                page_data['script_text'] = page_data.get('original_text', f"第{page_num}段内容")
                page_data['use_audio'] = True

        self.updated_pages_data = self.pages_data
        messagebox.showinfo("成功", f"文案保存成功，共 {len(scripts_dict)} 段")
        self.dialog.destroy()

    def cancel(self):
        """取消编辑"""
        self.dialog.destroy()


class BatchModifyDialog:
    """批量修改对话框"""
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("批量修改文案")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.result = None
        self.create_widgets()

    def create_widgets(self):
        """创建组件"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="批量修改选项", font=('Arial', 12, 'bold')).pack(pady=(0, 20))

        # 选项
        self.operation_var = tk.StringVar(value="prefix")

        ttk.Radiobutton(main_frame, text="添加统一前缀", variable=self.operation_var,
                       value="prefix").pack(anchor=tk.W, pady=5)
        ttk.Radiobutton(main_frame, text="添加统一后缀", variable=self.operation_var,
                       value="suffix").pack(anchor=tk.W, pady=5)
        ttk.Radiobutton(main_frame, text="替换特定文字", variable=self.operation_var,
                       value="replace").pack(anchor=tk.W, pady=5)

        # 输入框
        ttk.Label(main_frame, text="输入内容:").pack(anchor=tk.W, pady=(20, 5))
        self.text_entry = ttk.Entry(main_frame, width=40)
        self.text_entry.pack(fill=tk.X, pady=(0, 10))

        # 替换专用输入框
        self.replace_frame = ttk.Frame(main_frame)
        self.replace_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(self.replace_frame, text="替换为:").pack(anchor=tk.W)
        self.replace_entry = ttk.Entry(self.replace_frame, width=40)
        self.replace_entry.pack(fill=tk.X)

        # 初始隐藏替换框
        self.replace_frame.pack_forget()

        # 绑定选项变化
        self.operation_var.trace('w', self.on_operation_change)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="确定", command=self.confirm).pack(side=tk.RIGHT, padx=(0, 10))

    def on_operation_change(self, *args):
        """操作选项变化"""
        if self.operation_var.get() == "replace":
            self.replace_frame.pack(fill=tk.X, pady=(0, 10))
        else:
            self.replace_frame.pack_forget()

    def confirm(self):
        """确定修改"""
        operation = self.operation_var.get()
        text = self.text_entry.get().strip()

        if not text:
            messagebox.showwarning("警告", "请输入内容")
            return

        if operation == "replace":
            replace_text = self.replace_entry.get().strip()
            if not replace_text:
                messagebox.showwarning("警告", "请输入替换内容")
                return
            self.result = (operation, (text, replace_text))
        else:
            self.result = (operation, text)

        self.dialog.destroy()

    def cancel(self):
        """取消修改"""
        self.dialog.destroy()


def main():
    """主程序入口"""
    root = tk.Tk()
    app = VideoConverterGUI(root)

    # 设置关闭事件
    def on_closing():
        if app.is_processing:
            if messagebox.askyesno("确认", "正在处理中，确认退出？"):
                app.is_processing = False
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)

    def browse_file(self):
        """浏览选择文件"""
        filetypes = [
            ("所有支持的文件", "*.pdf;*.jpg;*.jpeg;*.png;*.bmp;*.tiff;*.tif;*.webp"),
            ("PDF文件", "*.pdf"),
            ("图片文件", "*.jpg;*.jpeg;*.png;*.bmp;*.tiff;*.tif;*.webp"),
            ("所有文件", "*.*")
        ]

        filename = filedialog.askopenfilename(
            title="选择PDF或图片文件",
            filetypes=filetypes,
            initialdir=os.getcwd()
        )

        if filename:
            self.selected_file = filename
            self.file_path_var.set(filename)

            # 显示文件信息
            file_path = Path(filename)
            file_size = file_path.stat().st_size / 1024 / 1024  # MB
            file_info = f"文件: {file_path.name} | 大小: {file_size:.1f} MB | 类型: {file_path.suffix.upper()}"
            self.file_info_var.set(file_info)

            # 启用预览按钮
            self.preview_btn.config(state='normal')
            self.convert_btn.config(state='normal')

            self.log_message(f"✅ 已选择文件: {file_path.name}")

    def browse_output_dir(self):
        """浏览选择输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir_var.get()
        )

        if directory:
            self.output_dir_var.set(directory)
            self.log_message(f"✅ 输出目录设置为: {directory}")

    def select_voice(self):
        """选择语音"""
        if self.voice_engine_var.get() == "Edge-TTS（高质量）":
            self.show_voice_selection_dialog()
        elif self.voice_engine_var.get() == "本地语音（pyttsx3）":
            messagebox.showinfo("语音设置", "本地语音将使用系统默认中文语音")
        else:
            messagebox.showinfo("语音设置", "已设置为无语音模式")

    def show_voice_selection_dialog(self):
        """显示语音选择对话框"""
        voice_dialog = VoiceSelectionDialog(self.root)
        self.root.wait_window(voice_dialog.dialog)

        if hasattr(voice_dialog, 'selected_voice') and voice_dialog.selected_voice:
            self.selected_voice = voice_dialog.selected_voice
            self.log_message(f"✅ 已选择语音: {voice_dialog.selected_voice_name}")

    def preview_split(self):
        """预览分割"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择文件")
            return

        self.log_message("🔍 开始预览分割...")

        # 在新线程中处理
        threading.Thread(target=self._preview_split_thread, daemon=True).start()

    def _preview_split_thread(self):
        """预览分割的线程函数"""
        try:
            # 创建转换器
            self.converter = EnhancedImageVideoConverter(self.deps)

            # 提取内容
            self.log_message("📖 正在提取文件内容...")
            self.pages_data = self.converter.extract_content(self.selected_file)

            if not self.pages_data:
                self.log_message("❌ 文件内容提取失败")
                return

            self.log_message(f"✅ 成功提取 {len(self.pages_data)} 个片段")

            # 根据分割方式处理
            split_method = self.split_method_var.get()
            if split_method == "手动分割（可视化）":
                self.root.after(0, self.show_manual_split_dialog)
            else:
                self.log_message("✅ 预览完成，可以开始转换")
                self.root.after(0, lambda: self.edit_script_btn.config(state='normal'))

        except Exception as e:
            self.log_message(f"❌ 预览失败: {e}")

    def show_manual_split_dialog(self):
        """显示手动分割对话框"""
        if not self.pages_data:
            messagebox.showwarning("警告", "没有可分割的内容")
            return

        # 获取原始图片
        original_img = self.pages_data[0]['image'] if self.pages_data else None
        if not original_img:
            messagebox.showerror("错误", "无法获取图片数据")
            return

        # 显示手动分割界面
        split_dialog = ManualSplitDialog(self.root, original_img,
                                        Path(self.selected_file).stem)
        self.root.wait_window(split_dialog.dialog)

        if hasattr(split_dialog, 'split_result') and split_dialog.split_result:
            self.pages_data = split_dialog.split_result
            self.log_message(f"✅ 手动分割完成，共 {len(self.pages_data)} 个片段")
            self.edit_script_btn.config(state='normal')

    def edit_scripts(self):
        """编辑文案"""
        if not self.pages_data:
            messagebox.showwarning("警告", "请先预览分割")
            return

        # 显示文案编辑对话框
        script_dialog = ScriptEditDialog(self.root, self.pages_data)
        self.root.wait_window(script_dialog.dialog)

        if hasattr(script_dialog, 'updated_pages_data'):
            self.pages_data = script_dialog.updated_pages_data
            self.log_message("✅ 文案编辑完成")

    def start_conversion(self):
        """开始转换"""
        if not self.selected_file:
            messagebox.showwarning("警告", "请先选择文件")
            return

        if not self.pages_data:
            messagebox.showwarning("警告", "请先预览分割")
            return

        # 确认开始转换
        result = messagebox.askyesno("确认",
                                   f"确认开始转换？\n"
                                   f"文件: {Path(self.selected_file).name}\n"
                                   f"片段数: {len(self.pages_data)}\n"
                                   f"语音引擎: {self.voice_engine_var.get()}")

        if not result:
            return

        # 设置界面状态
        self.is_processing = True
        self.convert_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress_var.set(0)

        # 在新线程中处理转换
        threading.Thread(target=self._conversion_thread, daemon=True).start()

    def _conversion_thread(self):
        """转换处理线程"""
        try:
            self.log_message("🎬 开始视频转换...")

            # 设置输出路径
            output_dir = Path(self.output_dir_var.get())
            output_dir.mkdir(exist_ok=True)

            file_name = Path(self.selected_file).stem
            output_path = output_dir / f"{file_name}_video.mp4"

            # 创建转换器并设置参数
            if not self.converter:
                self.converter = EnhancedImageVideoConverter(self.deps)

            # 设置语音引擎
            voice_engine = self.voice_engine_var.get()
            if voice_engine == "Edge-TTS（高质量）":
                self.converter.voice_engine = 'edge'
                if hasattr(self, 'selected_voice'):
                    self.converter.selected_voice = self.selected_voice
            elif voice_engine == "本地语音（pyttsx3）":
                self.converter.voice_engine = 'pyttsx3'
            else:
                self.converter.voice_engine = None

            # 开始转换
            success = self._create_video_with_progress(output_path)

            if success:
                self.log_message(f"🎉 转换成功！")
                self.log_message(f"输出文件: {output_path}")

                # 询问是否打开文件
                self.root.after(0, lambda: self._show_completion_dialog(output_path))
            else:
                self.log_message("❌ 转换失败")

        except Exception as e:
            self.log_message(f"❌ 转换过程出错: {e}")
        finally:
            # 恢复界面状态
            self.root.after(0, self._reset_ui_state)

    def _create_video_with_progress(self, output_path):
        """带进度显示的视频创建"""
        try:
            clips = []
            total_segments = len(self.pages_data)

            for i, page_data in enumerate(self.pages_data):
                if not self.is_processing:  # 检查是否被停止
                    return False

                page_num = page_data['page_num']
                self.log_message(f"处理第 {page_num} 段...")

                # 更新进度
                progress = (i / total_segments) * 90  # 90%用于处理片段
                self.root.after(0, lambda p=progress: self.progress_var.set(p))

                # 处理图片
                img = page_data['image']

                # 根据选择的处理方式处理图片
                image_method = self.image_process_var.get()
                if image_method == "居中裁剪":
                    img_processed = self.converter.crop_to_aspect_ratio(img, 16/9)
                elif image_method == "拉伸填充":
                    img_processed = self.converter.stretch_to_size(img, (1280, 720))
                elif image_method == "等比缩放":
                    img_processed = self.converter.scale_with_letterbox(img, (1280, 720))
                else:  # 智能适配
                    img_processed = self.converter.smart_resize(img, (1280, 720))

                # 添加段号标识
                img_processed = self.converter.add_segment_label(img_processed, page_num)

                # 保存图片
                img_path = self.converter.temp_dir / f"segment_{page_num}.png"
                img_processed.save(img_path)

                # 处理音频
                duration = 5
                audio_path = None

                if page_data.get('use_audio', True) and self.converter.voice_engine:
                    audio_path = self.converter.temp_dir / f"audio_{page_num}.wav"
                    script_text = page_data.get('script_text', f"第{page_num}段内容")

                    self.log_message(f"  🎵 生成语音...")

                    if self.converter.generate_speech(script_text, str(audio_path)):
                        try:
                            audio_clip = self.converter.deps['AudioFileClip'](str(audio_path))
                            duration = max(audio_clip.duration, 3)
                            audio_clip.close()
                            self.log_message(f"  ✅ 语音时长: {duration:.1f}秒")
                        except Exception as e:
                            self.log_message(f"  ⚠️ 音频处理失败: {e}")
                            duration = 5
                            audio_path = None
                    else:
                        self.log_message(f"  ⚠️ 语音生成失败，使用静音")
                        audio_path = None
                        duration = 5

                # 创建视频片段
                try:
                    video_clip = self.converter.deps['ImageClip'](str(img_path), duration=duration)

                    if audio_path and Path(audio_path).exists():
                        audio_clip = self.converter.deps['AudioFileClip'](str(audio_path))
                        video_clip = video_clip.set_audio(audio_clip)

                    clips.append(video_clip)
                    self.log_message(f"  ✅ 第 {page_num} 段完成")

                except Exception as e:
                    self.log_message(f"  ❌ 第 {page_num} 段失败: {e}")
                    continue

            if not clips:
                self.log_message("❌ 没有成功创建任何视频片段")
                return False

            # 合并视频
            self.log_message("正在合并视频...")
            self.root.after(0, lambda: self.progress_var.set(95))

            final_video = self.converter.deps['concatenate_videoclips'](clips)

            self.log_message("正在输出视频文件...")
            self.root.after(0, lambda: self.progress_var.set(98))

            final_video.write_videofile(
                str(output_path),
                fps=24,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )

            # 清理
            final_video.close()
            for clip in clips:
                clip.close()

            self.root.after(0, lambda: self.progress_var.set(100))
            return True

        except Exception as e:
            self.log_message(f"❌ 视频创建失败: {e}")
            return False

    def _show_completion_dialog(self, output_path):
        """显示完成对话框"""
        result = messagebox.askyesnocancel("转换完成",
                                         f"视频转换完成！\n\n"
                                         f"文件: {output_path.name}\n"
                                         f"位置: {output_path.parent}\n\n"
                                         f"是否打开文件？\n"
                                         f"（选择'否'打开文件夹）")

        if result is True:  # 打开文件
            os.startfile(str(output_path))
        elif result is False:  # 打开文件夹
            os.startfile(str(output_path.parent))

    def _reset_ui_state(self):
        """重置界面状态"""
        self.is_processing = False
        self.convert_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.progress_var.set(0)

    def stop_conversion(self):
        """停止转换"""
        if messagebox.askyesno("确认", "确认停止转换？"):
            self.is_processing = False
            self.log_message("⏹️ 用户停止转换")
            self._reset_ui_state()

    def open_output_directory(self):
        """打开输出目录"""
        output_dir = self.output_dir_var.get()
        if os.path.exists(output_dir):
            os.startfile(output_dir)
        else:
            messagebox.showwarning("警告", f"输出目录不存在: {output_dir}")
