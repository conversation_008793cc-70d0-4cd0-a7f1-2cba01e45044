#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除片段功能
"""

import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_image_with_sections():
    """创建包含多个明显区域的测试图片"""
    width = 800
    height = 1000
    
    # 创建图片
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体
    try:
        title_font = ImageFont.truetype("arial.ttf", 32)
        content_font = ImageFont.truetype("arial.ttf", 20)
        small_font = ImageFont.truetype("arial.ttf", 16)
    except:
        title_font = ImageFont.load_default()
        content_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 定义不同类型的内容区域
    sections = [
        (0, 0, 800, 150, 'lightblue', '标题区域', '这是文档的标题部分，通常需要保留'),
        (0, 150, 800, 250, 'red', '广告区域', '这是广告内容，通常需要删除'),
        (0, 250, 800, 400, 'lightgreen', '重要内容1', '这是重要的内容区域，需要保留'),
        (0, 400, 800, 500, 'orange', '无关内容', '这是无关的内容，可以删除'),
        (0, 500, 800, 650, 'lightyellow', '重要内容2', '这是另一个重要内容区域，需要保留'),
        (0, 650, 800, 750, 'purple', '推广信息', '这是推广信息，通常需要删除'),
        (0, 750, 800, 900, 'lightcyan', '重要内容3', '这是最后的重要内容，需要保留'),
        (0, 900, 800, 1000, 'brown', '页脚信息', '这是页脚版权信息，可以删除')
    ]
    
    for x1, y1, x2, y2, color, title, description in sections:
        # 绘制区域背景
        draw.rectangle([x1, y1, x2, y2], fill=color, outline='black', width=3)
        
        # 添加区域标题
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2
        draw.text((title_x, y1 + 20), title, fill='black', font=title_font)
        
        # 添加描述文字
        desc_lines = description.split('，')
        for i, line in enumerate(desc_lines):
            desc_y = y1 + 70 + i * 25
            if desc_y < y2 - 20:
                draw.text((50, desc_y), line, fill='darkblue', font=content_font)
        
        # 为需要删除的区域添加特殊标记
        if color in ['red', 'orange', 'purple', 'brown']:
            # 添加删除建议标记
            draw.text((width - 150, y1 + 10), "🗑️ 建议删除", fill='darkred', font=content_font)
        else:
            # 添加保留建议标记
            draw.text((width - 150, y1 + 10), "✅ 建议保留", fill='darkgreen', font=content_font)
    
    # 添加分割线建议
    suggested_splits = [150, 250, 400, 500, 650, 750, 900]
    for y in suggested_splits:
        draw.line([0, y, width, y], fill='gray', width=2, dash=(10, 5))
        draw.text((10, y - 15), f"建议分割线 Y={y}", fill='gray', font=small_font)
    
    # 添加坐标网格
    for x in range(0, width, 100):
        draw.line([x, 0, x, height], fill='lightgray', width=1)
        if x > 0:
            draw.text((x - 15, 5), str(x), fill='gray', font=small_font)
    
    for y in range(0, height, 100):
        draw.line([0, y, width, y], fill='lightgray', width=1)
        if y > 0:
            draw.text((5, y - 15), str(y), fill='gray', font=small_font)
    
    return img

def test_delete_segments_functionality():
    """测试删除片段功能"""
    print("创建测试图片...")
    test_img = create_test_image_with_sections()
    
    print(f"测试图片尺寸: {test_img.width} x {test_img.height}")
    
    try:
        from gui_video_converter import ManualSplitDialog
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("打开手动分割对话框...")
        print("\n🎯 删除片段功能测试说明:")
        print("1. 首先添加分割点（建议在Y=150, 250, 400, 500, 650, 750, 900位置）")
        print("2. 点击'删除片段模式'按钮")
        print("3. 点击要删除的片段（红色、橙色、紫色、棕色区域）：")
        print("   • 片段2：广告区域 (Y=150-250)")
        print("   • 片段4：无关内容 (Y=400-500)")
        print("   • 片段6：推广信息 (Y=650-750)")
        print("   • 片段8：页脚信息 (Y=900-1000)")
        print("4. 被删除的片段会显示为红色")
        print("5. 保留的片段会显示为绿色")
        print("6. 可以点击片段切换删除/保留状态")
        print("7. 在删除模式下仍可以添加、移动、删除分割点")
        print("8. 完成后点击'完成分割'")
        print("\n✨ 被删除的片段将完全不出现在最终视频中！")
        
        # 创建手动分割对话框
        dialog = ManualSplitDialog(root, test_img, "删除片段测试")
        
        # 等待对话框关闭
        root.wait_window(dialog.dialog)
        
        # 检查结果
        if hasattr(dialog, 'split_result') and dialog.split_result:
            print(f"\n✅ 分割和删除成功！")
            print(f"最终保留片段数: {len(dialog.split_result)}")
            print(f"删除的片段索引: {dialog.deleted_segments}")
            
            # 保存结果图片以便查看
            for i, segment in enumerate(dialog.split_result):
                img = segment['image']
                output_path = f"delete_test_result_segment_{i+1}.png"
                img.save(output_path)
                print(f"  保留片段 {i+1}: {img.width}x{img.height}px -> {output_path}")
            
            # 计算原始片段总数
            all_points = [0] + dialog.split_points + [dialog.image.height]
            total_original_segments = len(all_points) - 1
            deleted_count = len(dialog.deleted_segments)
            
            print(f"\n📊 统计信息:")
            print(f"  原始片段总数: {total_original_segments}")
            print(f"  删除片段数: {deleted_count}")
            print(f"  保留片段数: {len(dialog.split_result)}")
            print(f"  删除的片段: {sorted(dialog.deleted_segments)}")
            
            print(f"\n🎉 成功！删除的片段已完全从视频中移除！")
        else:
            print("❌ 分割被取消或失败")
        
        root.destroy()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保gui_video_converter.py在同一目录下")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("删除片段功能测试")
    print("=" * 30)
    
    print("功能说明:")
    print("🗑️ 删除片段模式:")
    print("  - 点击片段：标记/取消删除片段")
    print("  - 红色片段：将被删除的片段")
    print("  - 绿色片段：保留的片段")
    print("  - 支持在删除模式下编辑分割点")
    print("  - 删除的片段完全不出现在最终视频中")
    print()
    print("📐 分割功能（在删除模式下仍可用）:")
    print("  - 单击空白处：添加分割点")
    print("  - 单击分割线：选中分割线")
    print("  - 拖拽分割线：移动位置")
    print("  - 双击分割线：删除分割点")
    print()
    
    choice = input("是否开始测试？(y/n): ").lower()
    if choice == 'y':
        test_delete_segments_functionality()
    else:
        print("测试取消")

if __name__ == "__main__":
    main()
