# 长图转视频工具 - 使用指南

## 🎯 主要功能

### 1. 手动分割功能
- **可视化分割界面**：点击图片添加分割点
- **拖拽移动**：选中分割线后可拖拽移动位置
- **双击删除**：双击分割线可删除分割点
- **最小间距**：自动保持20像素最小间距
- **自动建议**：可自动生成等间距分割点

### 2. 智能图片处理
避免不同高度片段产生畸变：
- **智能适配**（推荐）：保持比例，添加合适背景
- **居中裁剪**：裁剪到16:9比例
- **拉伸填充**：强制拉伸（可能畸变）
- **等比缩放**：保持原比例，上下留黑边

### 3. 多种语音选择
Edge-TTS提供20种中文语音：

#### 女声选项（16种）
1. 晓晓 - 温柔甜美（默认推荐）
2. 晓伊 - 清新自然
3. 晓辰 - 知性优雅
4. 晓涵 - 亲切温暖
5. 晓梦 - 可爱活泼
6. 晓墨 - 成熟知性
7. 晓秋 - 温和亲和
8. 晓睿 - 聪慧理性
9. 晓双 - 清脆明亮
10. 晓萱 - 优雅大方
11. 晓颜 - 甜美可人
12. 晓悠 - 悠扬动听
13. 晓甄 - 端庄典雅

#### 男声选项（7种）
1. 云希 - 成熟稳重
2. 云扬 - 专业播音
3. 云健 - 活力阳光
4. 云枫 - 磁性深沉
5. 云皓 - 清朗有力
6. 云夏 - 热情洋溢
7. 云野 - 自然随性

## 🚀 使用步骤

### 1. 准备文件
支持的格式：
- 图片：JPG, PNG, BMP, TIFF, WEBP
- 文档：PDF

### 2. 运行程序
```bash
python long_image_to_video.py [文件名]
```

### 3. 选择分割方式
对于长图，推荐选择：
- **选项5：手动确定分割点（可视化）**

### 4. 手动分割操作
- 单击空白处：添加分割点
- 单击分割线：选中（变蓝色）
- 拖拽分割线：移动位置
- 双击分割线：删除
- 使用控制按钮：删除、清除、自动建议

### 5. 编辑文案
- 查看自动生成的文案
- 可以编辑文案文件
- 支持批量修改

### 6. 选择语音
- 选择Edge-TTS获得高质量语音
- 从20种中文语音中选择
- 可设置语速和音调

### 7. 选择图片处理方式
- **智能适配**：最佳选择，避免畸变
- 根据内容选择合适的处理方式

## 🔧 故障排除

### Edge-TTS语音问题
如果遇到"NoAudioReceived"错误：

1. **网络问题**
   - 检查网络连接
   - 尝试使用VPN
   - 检查防火墙设置

2. **文本问题**
   - 程序会自动简化文本
   - 移除特殊字符
   - 使用备用语音方案

3. **备用方案**
   - 程序自动使用最稳定的语音
   - 如果仍失败，选择本地语音引擎
   - 或选择无语音模式

### 图片处理问题
- 确保图片文件完整
- 支持的最大尺寸建议不超过10000像素
- 复杂图片可能需要更多处理时间

## 💡 使用技巧

### 1. 分割技巧
- 在内容自然分界处设置分割点
- 避免在文字中间分割
- 保持每段内容的完整性

### 2. 语音选择
- **教育内容**：选择晓睿、云扬（专业）
- **娱乐内容**：选择晓梦、云夏（活泼）
- **商务内容**：选择晓墨、云希（成熟）
- **温馨内容**：选择晓涵、晓秋（温和）

### 3. 文案优化
- 保持每段文案长度适中（50-200字）
- 使用简洁明了的语言
- 添加适当的停顿标点

### 4. 视频质量
- 选择智能适配获得最佳视觉效果
- 确保分割点设置合理
- 预览文案确保内容准确

## 📁 输出文件

程序会生成：
- `[文件名]_video.mp4`：最终视频文件
- `[文件名]_scripts.txt`：文案文件
- `output/`：输出目录

## 🆘 技术支持

如果遇到问题：
1. 查看控制台输出的详细错误信息
2. 尝试使用测试脚本诊断问题
3. 检查网络连接和依赖安装
4. 使用备用的本地语音引擎

---

**版本**：增强版 v2.0  
**更新**：添加手动分割、多语音选择、智能图片处理
