#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Edge-TTS语音选项
"""

import subprocess
import sys
from pathlib import Path

def check_edge_tts():
    """检查Edge-TTS是否安装"""
    try:
        result = subprocess.run(['edge-tts', '--help'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_edge_tts():
    """安装Edge-TTS"""
    try:
        print("正在安装Edge-TTS...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "edge-tts"])
        print("✅ Edge-TTS安装成功")
        return True
    except Exception as e:
        print(f"❌ Edge-TTS安装失败: {e}")
        return False

def list_available_voices():
    """列出所有可用的中文语音"""
    try:
        result = subprocess.run(['edge-tts', '--list-voices'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            chinese_voices = []
            
            for line in lines:
                if 'zh-CN' in line and 'Neural' in line:
                    chinese_voices.append(line.strip())
            
            return chinese_voices
        else:
            return []
    except Exception as e:
        print(f"获取语音列表失败: {e}")
        return []

def test_voice_sample(voice_id, voice_name, text="这是一个语音测试样本。"):
    """测试单个语音"""
    output_file = f"test_{voice_id.replace('-', '_')}.wav"
    
    try:
        cmd = [
            "edge-tts",
            "--voice", voice_id,
            "--text", text,
            "--write-media", output_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and Path(output_file).exists():
            file_size = Path(output_file).stat().st_size
            print(f"✅ {voice_name}: {output_file} ({file_size} bytes)")
            return True
        else:
            print(f"❌ {voice_name}: 生成失败")
            return False
            
    except Exception as e:
        print(f"❌ {voice_name}: {e}")
        return False

def main():
    print("Edge-TTS 中文语音测试工具")
    print("=" * 40)
    
    # 检查Edge-TTS
    if not check_edge_tts():
        print("❌ Edge-TTS未安装")
        if input("是否安装Edge-TTS？(y/n): ").lower() == 'y':
            if not install_edge_tts():
                return
        else:
            return
    
    print("✅ Edge-TTS已安装")
    
    # 我们的语音列表
    voices = [
        ('zh-CN-XiaoxiaoNeural', '晓晓 (女声) - 温柔甜美'),
        ('zh-CN-YunxiNeural', '云希 (男声) - 成熟稳重'),
        ('zh-CN-YunyangNeural', '云扬 (男声) - 专业播音'),
        ('zh-CN-XiaoyiNeural', '晓伊 (女声) - 清新自然'),
        ('zh-CN-YunjianNeural', '云健 (男声) - 活力阳光'),
        ('zh-CN-XiaochenNeural', '晓辰 (女声) - 知性优雅'),
        ('zh-CN-XiaohanNeural', '晓涵 (女声) - 亲切温暖'),
        ('zh-CN-XiaomengNeural', '晓梦 (女声) - 可爱活泼'),
        ('zh-CN-XiaomoNeural', '晓墨 (女声) - 成熟知性'),
        ('zh-CN-XiaoqiuNeural', '晓秋 (女声) - 温和亲和'),
        ('zh-CN-XiaoruiNeural', '晓睿 (女声) - 聪慧理性'),
        ('zh-CN-XiaoshuangNeural', '晓双 (女声) - 清脆明亮'),
        ('zh-CN-XiaoxuanNeural', '晓萱 (女声) - 优雅大方'),
        ('zh-CN-XiaoyanNeural', '晓颜 (女声) - 甜美可人'),
        ('zh-CN-XiaoyouNeural', '晓悠 (女声) - 悠扬动听'),
        ('zh-CN-XiaozhenNeural', '晓甄 (女声) - 端庄典雅'),
        ('zh-CN-YunfengNeural', '云枫 (男声) - 磁性深沉'),
        ('zh-CN-YunhaoNeural', '云皓 (男声) - 清朗有力'),
        ('zh-CN-YunxiaNeural', '云夏 (男声) - 热情洋溢'),
        ('zh-CN-YunyeNeural', '云野 (男声) - 自然随性')
    ]
    
    print(f"\n🎭 可用的中文语音 ({len(voices)} 个):")
    print("-" * 50)
    
    female_voices = [v for v in voices if '女声' in v[1]]
    male_voices = [v for v in voices if '男声' in v[1]]
    
    print(f"👩 女声 ({len(female_voices)} 个):")
    for i, (voice_id, desc) in enumerate(female_voices, 1):
        print(f"  {i:2d}. {desc}")
    
    print(f"\n👨 男声 ({len(male_voices)} 个):")
    for i, (voice_id, desc) in enumerate(male_voices, 1):
        print(f"  {i:2d}. {desc}")
    
    # 选择测试模式
    print(f"\n🧪 测试选项:")
    print("1. 测试所有语音")
    print("2. 测试指定语音")
    print("3. 仅显示语音列表")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        print(f"\n开始测试所有语音...")
        test_text = input("输入测试文本 (默认: '大家好，这是语音测试。'): ").strip()
        if not test_text:
            test_text = "大家好，这是语音测试。"
        
        success_count = 0
        for voice_id, desc in voices:
            if test_voice_sample(voice_id, desc, test_text):
                success_count += 1
        
        print(f"\n📊 测试完成: {success_count}/{len(voices)} 个语音测试成功")
        
    elif choice == "2":
        print(f"\n选择要测试的语音:")
        for i, (voice_id, desc) in enumerate(voices, 1):
            print(f"{i:2d}. {desc}")
        
        try:
            voice_choice = int(input(f"请选择 (1-{len(voices)}): ")) - 1
            if 0 <= voice_choice < len(voices):
                voice_id, desc = voices[voice_choice]
                test_text = input("输入测试文本 (默认: '大家好，这是语音测试。'): ").strip()
                if not test_text:
                    test_text = "大家好，这是语音测试。"
                
                print(f"\n测试语音: {desc}")
                test_voice_sample(voice_id, desc, test_text)
            else:
                print("❌ 选择无效")
        except ValueError:
            print("❌ 输入无效")
    
    elif choice == "3":
        print("✅ 语音列表已显示")
    
    print(f"\n💡 使用提示:")
    print("1. 在 long_image_to_video.py 中选择 Edge-TTS 时，会显示这些语音选项")
    print("2. 不同语音有不同的特色，可以根据内容选择合适的语音")
    print("3. 女声通常更适合温和的内容，男声更适合正式的内容")

if __name__ == "__main__":
    main()
