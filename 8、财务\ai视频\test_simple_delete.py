#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的删除片段功能
"""

import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_simple_test_image():
    """创建简单的测试图片"""
    width = 600
    height = 800
    
    # 创建图片
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        content_font = ImageFont.truetype("arial.ttf", 16)
    except:
        title_font = ImageFont.load_default()
        content_font = ImageFont.load_default()
    
    # 创建5个明显的区域
    sections = [
        (0, 0, 600, 160, 'lightblue', '第1段 - 标题内容', '保留'),
        (0, 160, 600, 320, 'lightcoral', '第2段 - 广告内容', '删除'),
        (0, 320, 600, 480, 'lightgreen', '第3段 - 重要内容', '保留'),
        (0, 480, 600, 640, 'lightyellow', '第4段 - 推广信息', '删除'),
        (0, 640, 600, 800, 'lightcyan', '第5段 - 结论内容', '保留')
    ]
    
    for x1, y1, x2, y2, color, title, action in sections:
        # 绘制区域背景
        draw.rectangle([x1, y1, x2, y2], fill=color, outline='black', width=2)
        
        # 添加标题
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (width - title_width) // 2
        draw.text((title_x, y1 + 30), title, fill='black', font=title_font)
        
        # 添加操作建议
        action_text = f"建议: {action}"
        action_color = 'darkgreen' if action == '保留' else 'darkred'
        draw.text((50, y1 + 70), action_text, fill=action_color, font=content_font)
        
        # 添加一些内容
        content_lines = [
            "这里是一些示例内容",
            "用于测试分割和删除功能",
            "您可以点击片段来切换状态"
        ]
        
        for i, line in enumerate(content_lines):
            draw.text((50, y1 + 100 + i * 20), line, fill='darkblue', font=content_font)
    
    # 添加分割线建议
    suggested_splits = [160, 320, 480, 640]
    for y in suggested_splits:
        draw.line([0, y, width, y], fill='gray', width=1, dash=(5, 5))
        draw.text((10, y - 15), f"建议分割线 Y={y}", fill='gray')
    
    return img

def test_simple_delete_functionality():
    """测试简化的删除功能"""
    print("创建简单测试图片...")
    test_img = create_simple_test_image()
    
    print(f"测试图片尺寸: {test_img.width} x {test_img.height}")
    
    try:
        from gui_video_converter import ManualSplitDialog
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("打开手动分割对话框...")
        print("\n🎯 简化删除功能测试说明:")
        print("1. 界面会自动显示片段覆盖层（绿色=保留，红色=删除）")
        print("2. 首先添加分割点（建议在Y=160, 320, 480, 640位置点击）")
        print("3. 直接点击片段区域来切换保留/删除状态：")
        print("   • 第1段（蓝色）：标题内容 - 建议保留")
        print("   • 第2段（红色）：广告内容 - 建议删除")
        print("   • 第3段（绿色）：重要内容 - 建议保留")
        print("   • 第4段（黄色）：推广信息 - 建议删除")
        print("   • 第5段（青色）：结论内容 - 建议保留")
        print("4. 绿色覆盖层 = 保留的片段")
        print("5. 红色覆盖层 = 删除的片段")
        print("6. 可以随时添加、移动、删除分割点")
        print("7. 完成后点击'完成分割'")
        print("\n✨ 被删除的片段将完全不出现在最终视频中！")
        
        # 创建手动分割对话框
        dialog = ManualSplitDialog(root, test_img, "简化删除测试")
        
        # 等待对话框关闭
        root.wait_window(dialog.dialog)
        
        # 检查结果
        if hasattr(dialog, 'split_result') and dialog.split_result:
            print(f"\n✅ 分割和删除成功！")
            print(f"最终保留片段数: {len(dialog.split_result)}")
            print(f"删除的片段索引: {sorted(dialog.deleted_segments)}")
            
            # 保存结果图片以便查看
            for i, segment in enumerate(dialog.split_result):
                img = segment['image']
                output_path = f"simple_delete_result_segment_{i+1}.png"
                img.save(output_path)
                print(f"  保留片段 {i+1}: {img.width}x{img.height}px -> {output_path}")
            
            # 计算统计信息
            all_points = [0] + dialog.split_points + [dialog.image.height]
            total_original_segments = len(all_points) - 1
            deleted_count = len(dialog.deleted_segments)
            
            print(f"\n📊 统计信息:")
            print(f"  原始片段总数: {total_original_segments}")
            print(f"  删除片段数: {deleted_count}")
            print(f"  保留片段数: {len(dialog.split_result)}")
            
            if dialog.deleted_segments:
                print(f"  删除的片段: {sorted(dialog.deleted_segments)}")
            else:
                print(f"  没有删除任何片段")
            
            print(f"\n🎉 成功！删除的片段已完全从视频中移除！")
        else:
            print("❌ 分割被取消或失败")
        
        root.destroy()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保gui_video_converter.py在同一目录下")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("简化删除片段功能测试")
    print("=" * 30)
    
    print("功能说明:")
    print("🎯 一体化界面:")
    print("  - 自动显示片段覆盖层")
    print("  - 绿色片段：保留的片段")
    print("  - 红色片段：删除的片段")
    print("  - 无需切换模式")
    print()
    print("🖱️ 操作方式:")
    print("  - 点击空白处：添加分割点")
    print("  - 点击分割线：选中/移动分割线")
    print("  - 双击分割线：删除分割线")
    print("  - 点击片段区域：切换保留/删除状态")
    print("  - 拖拽分割线：调整位置")
    print()
    print("✨ 优势:")
    print("  - 直观可视：一目了然看到哪些片段会被保留/删除")
    print("  - 操作简单：点击片段即可切换状态")
    print("  - 实时反馈：立即看到操作结果")
    print("  - 完全兼容：所有分割功能正常使用")
    print()
    
    choice = input("是否开始测试？(y/n): ").lower()
    if choice == 'y':
        test_simple_delete_functionality()
    else:
        print("测试取消")

if __name__ == "__main__":
    main()
