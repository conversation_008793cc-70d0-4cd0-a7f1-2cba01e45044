#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转视频工具 - 完美版
支持文案生成、预览、编辑和本地文案选择
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import time
import json

def check_and_install_fitz():
    """检查并安装PyMuPDF"""
    try:
        import fitz
        return fitz
    except ImportError:
        print("正在安装PyMuPDF...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PyMuPDF"])
            import fitz
            print("✅ PyMuPDF安装成功")
            return fitz
        except Exception as e:
            print(f"❌ PyMuPDF安装失败: {e}")
            return None

def check_dependencies():
    """检查依赖"""
    deps = {}
    
    # 检查PyMuPDF
    fitz = check_and_install_fitz()
    if not fitz:
        return None
    deps['fitz'] = fitz
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        deps.update({'Image': Image, 'ImageDraw': ImageDraw, 'ImageFont': ImageFont})
    except ImportError:
        print("❌ 缺少Pillow，请运行: pip install Pillow")
        return None
    
    try:
        import pyttsx3
        deps['pyttsx3'] = pyttsx3
    except ImportError:
        print("⚠️ 缺少pyttsx3，将跳过本地语音功能")
        deps['pyttsx3'] = None
    
    # 检查Edge-TTS
    try:
        import subprocess
        result = subprocess.run(['pip', 'show', 'edge-tts'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("正在安装Edge-TTS...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "edge-tts"])
        deps['edge_tts'] = True
    except:
        deps['edge_tts'] = False
    
    try:
        from moviepy.editor import ImageClip, AudioFileClip, concatenate_videoclips
        deps.update({
            'ImageClip': ImageClip,
            'AudioFileClip': AudioFileClip,
            'concatenate_videoclips': concatenate_videoclips
        })
    except ImportError:
        print("❌ MoviePy导入失败，请先运行: python fix_moviepy.py")
        return None
    
    return deps

class PerfectPDFConverter:
    def __init__(self, deps):
        self.deps = deps
        self.temp_dir = Path(tempfile.mkdtemp())
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
        # 文案相关
        self.scripts_file = None
        self.use_local_scripts = False
        self.voice_engine = None
        
    def extract_pdf_content(self, pdf_path):
        """提取PDF内容"""
        print("📖 正在提取PDF内容...")
        
        try:
            doc = self.deps['fitz'].open(str(pdf_path))
            pages_data = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # 提取文本
                text = page.get_text()
                
                # 转换为图片
                mat = self.deps['fitz'].Matrix(2.0, 2.0)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("ppm")
                
                from io import BytesIO
                img = self.deps['Image'].open(BytesIO(img_data))
                
                pages_data.append({
                    'page_num': page_num + 1,
                    'image': img,
                    'original_text': text.strip() if text.strip() else f"第{page_num + 1}页内容",
                    'script_text': '',
                    'use_audio': True
                })
                
                pix = None
            
            doc.close()
            print(f"✅ 成功提取 {len(pages_data)} 页内容")
            return pages_data
            
        except Exception as e:
            print(f"❌ PDF内容提取失败: {e}")
            return []
    
    def generate_initial_scripts(self, pages_data, pdf_name):
        """生成初始文案"""
        print("\n📝 生成初始文案...")
        
        # 创建文案文件
        scripts_filename = f"{pdf_name}_scripts.txt"
        scripts_path = Path(scripts_filename)
        
        # 检查是否已存在文案文件
        if scripts_path.exists():
            print(f"✅ 发现现有文案文件: {scripts_filename}")
            choice = input("是否使用现有文案？(y/n): ").lower()
            if choice == 'y':
                return self.load_scripts_from_file(pages_data, scripts_path)
        
        # 生成新文案
        print("正在生成文案...")
        with open(scripts_path, 'w', encoding='utf-8') as f:
            f.write(f"# {pdf_name} - 视频文案\n")
            f.write("# 格式：每页一行，以 [页码] 开头\n")
            f.write("# 示例：[1] 这是第一页的讲解内容\n")
            f.write("# 空行或以#开头的行将被忽略\n\n")
            
            for page_data in pages_data:
                page_num = page_data['page_num']
                original_text = page_data['original_text']
                
                # 处理文本：清理和优化
                cleaned_text = self.clean_text_for_speech(original_text)
                
                f.write(f"[{page_num}] {cleaned_text}\n\n")
                
                # 同时更新页面数据
                page_data['script_text'] = cleaned_text
        
        print(f"✅ 文案已生成到: {scripts_filename}")
        self.scripts_file = scripts_path
        return pages_data
    
    def clean_text_for_speech(self, text):
        """清理文本，使其更适合语音合成"""
        if not text or len(text.strip()) < 10:
            return "本页内容请参考屏幕显示。"
        
        # 移除多余的空白字符
        text = ' '.join(text.split())
        
        # 限制长度
        if len(text) > 300:
            # 尝试在句号处截断
            sentences = text.split('。')
            result = ""
            for sentence in sentences:
                if len(result + sentence + '。') <= 300:
                    result += sentence + '。'
                else:
                    break
            if result:
                text = result
            else:
                text = text[:300] + "..."
        
        # 添加适当的停顿标记
        text = text.replace('。', '。 ')
        text = text.replace('，', '， ')
        
        return text.strip()
    
    def load_scripts_from_file(self, pages_data, scripts_path):
        """从文件加载文案"""
        try:
            with open(scripts_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 解析文案
            scripts_dict = {}
            for line in lines:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # 查找页码标记 [数字]
                if line.startswith('[') and ']' in line:
                    try:
                        end_bracket = line.index(']')
                        page_num = int(line[1:end_bracket])
                        script_text = line[end_bracket + 1:].strip()
                        scripts_dict[page_num] = script_text
                    except (ValueError, IndexError):
                        continue
            
            # 应用到页面数据
            for page_data in pages_data:
                page_num = page_data['page_num']
                if page_num in scripts_dict:
                    page_data['script_text'] = scripts_dict[page_num]
                else:
                    page_data['script_text'] = page_data['original_text']
            
            print(f"✅ 成功加载 {len(scripts_dict)} 页文案")
            return pages_data
            
        except Exception as e:
            print(f"❌ 文案加载失败: {e}")
            return pages_data
    
    def preview_and_edit_scripts(self, pages_data):
        """预览和编辑文案"""
        print("\n" + "="*60)
        print("📋 文案预览和编辑")
        print("="*60)
        
        # 显示所有文案预览
        print("\n当前文案预览:")
        print("-" * 40)
        for page_data in pages_data:
            page_num = page_data['page_num']
            script = page_data['script_text']
            print(f"[{page_num}] {script[:80]}{'...' if len(script) > 80 else ''}")
        
        print("\n选择操作:")
        print("1. 使用当前文案（继续生成视频）")
        print("2. 打开文案文件进行编辑")
        print("3. 重新生成文案")
        print("4. 批量修改文案")
        print("5. 设置无语音模式")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            print("✅ 使用当前文案")
            return pages_data
            
        elif choice == "2":
            if self.scripts_file and self.scripts_file.exists():
                print(f"\n📝 请编辑文案文件: {self.scripts_file}")
                print("编辑完成后按回车继续...")
                input()
                # 重新加载文案
                return self.load_scripts_from_file(pages_data, self.scripts_file)
            else:
                print("❌ 文案文件不存在")
                return pages_data
                
        elif choice == "3":
            print("重新生成文案...")
            return self.regenerate_scripts(pages_data)
            
        elif choice == "4":
            return self.batch_modify_scripts(pages_data)
            
        elif choice == "5":
            for page_data in pages_data:
                page_data['use_audio'] = False
                page_data['script_text'] = f"第{page_data['page_num']}页"
            print("✅ 设置为无语音模式")
            return pages_data
            
        else:
            print("使用当前文案")
            return pages_data
    
    def regenerate_scripts(self, pages_data):
        """重新生成文案"""
        print("选择文案生成方式:")
        print("1. 使用原始PDF文本")
        print("2. 使用简化描述")
        print("3. 使用模板格式")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "2":
            # 简化描述
            for page_data in pages_data:
                page_data['script_text'] = f"现在我们来看第{page_data['page_num']}页的内容。"
        elif choice == "3":
            # 模板格式
            template = input("输入模板（用{page}表示页码）: ")
            for page_data in pages_data:
                page_data['script_text'] = template.replace("{page}", str(page_data['page_num']))
        else:
            # 使用原始文本
            for page_data in pages_data:
                page_data['script_text'] = self.clean_text_for_speech(page_data['original_text'])
        
        # 更新文案文件
        if self.scripts_file:
            self.save_scripts_to_file(pages_data)
        
        return pages_data
    
    def batch_modify_scripts(self, pages_data):
        """批量修改文案"""
        print("批量修改选项:")
        print("1. 添加统一前缀")
        print("2. 添加统一后缀")
        print("3. 替换特定文字")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            prefix = input("输入前缀: ")
            for page_data in pages_data:
                page_data['script_text'] = f"{prefix} {page_data['script_text']}"
        elif choice == "2":
            suffix = input("输入后缀: ")
            for page_data in pages_data:
                page_data['script_text'] = f"{page_data['script_text']} {suffix}"
        elif choice == "3":
            old_text = input("要替换的文字: ")
            new_text = input("替换为: ")
            for page_data in pages_data:
                page_data['script_text'] = page_data['script_text'].replace(old_text, new_text)
        
        # 更新文案文件
        if self.scripts_file:
            self.save_scripts_to_file(pages_data)
        
        return pages_data
    
    def save_scripts_to_file(self, pages_data):
        """保存文案到文件"""
        if not self.scripts_file:
            return
        
        try:
            with open(self.scripts_file, 'w', encoding='utf-8') as f:
                f.write(f"# 视频文案 - 已更新\n")
                f.write("# 格式：每页一行，以 [页码] 开头\n\n")
                
                for page_data in pages_data:
                    page_num = page_data['page_num']
                    script_text = page_data['script_text']
                    f.write(f"[{page_num}] {script_text}\n\n")
            
            print(f"✅ 文案已更新到: {self.scripts_file}")
        except Exception as e:
            print(f"❌ 文案保存失败: {e}")
    
    def setup_voice_engine(self):
        """设置语音引擎"""
        print("\n🎤 语音设置")
        print("=" * 20)
        
        use_voice = input("是否使用语音讲解？(y/n): ").lower()
        if use_voice != 'y':
            print("✅ 设置为无语音模式")
            return None
        
        print("\n选择语音引擎:")
        engines = []
        
        if self.deps['edge_tts']:
            engines.append(('edge', 'Edge-TTS (微软) - 高质量自然语音'))
        
        if self.deps['pyttsx3']:
            engines.append(('pyttsx3', 'pyttsx3 (本地) - 快速离线语音'))
        
        if not engines:
            print("❌ 没有可用的语音引擎")
            return None
        
        for i, (key, name) in enumerate(engines, 1):
            print(f"{i}. {name}")
        
        try:
            choice = int(input(f"请选择 (1-{len(engines)}): ")) - 1
            if 0 <= choice < len(engines):
                self.voice_engine = engines[choice][0]
                print(f"✅ 已选择: {engines[choice][1]}")
                return self.voice_engine
        except ValueError:
            pass
        
        # 默认选择第一个
        self.voice_engine = engines[0][0]
        print(f"✅ 使用默认引擎: {engines[0][1]}")
        return self.voice_engine
    
    def generate_speech(self, text, output_path):
        """生成语音"""
        if self.voice_engine == 'edge':
            return self.generate_edge_speech(text, output_path)
        elif self.voice_engine == 'pyttsx3':
            return self.generate_pyttsx3_speech(text, output_path)
        return False
    
    def generate_edge_speech(self, text, output_path):
        """使用Edge-TTS生成语音"""
        try:
            import subprocess
            
            cmd = [
                "edge-tts",
                "--voice", "zh-CN-XiaoxiaoNeural",
                "--text", text,
                "--write-media", str(output_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0 and Path(output_path).exists()
            
        except Exception as e:
            print(f"Edge-TTS生成失败: {e}")
            return False
    
    def generate_pyttsx3_speech(self, text, output_path):
        """使用pyttsx3生成语音"""
        try:
            engine = self.deps['pyttsx3'].init()
            engine.setProperty('rate', 160)
            engine.setProperty('volume', 0.9)
            
            # 选择中文语音
            voices = engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'mandarin' in voice.name.lower():
                    engine.setProperty('voice', voice.id)
                    break
            
            engine.save_to_file(text, str(output_path))
            engine.runAndWait()
            
            # 等待文件生成
            timeout = 15
            while not Path(output_path).exists() and timeout > 0:
                time.sleep(0.5)
                timeout -= 1
            
            return Path(output_path).exists()
        except Exception as e:
            print(f"pyttsx3生成失败: {e}")
            return False
    
    def create_video(self, pages_data, output_path):
        """创建视频"""
        print("\n🎬 开始创建视频...")
        clips = []
        
        for page_data in pages_data:
            page_num = page_data['page_num']
            print(f"  处理第 {page_num} 页...")
            
            # 处理图片
            img = page_data['image']
            img_resized = img.resize((1280, 720), self.deps['Image'].Resampling.LANCZOS)
            
            # 添加页码
            draw = self.deps['ImageDraw'].Draw(img_resized)
            try:
                font = self.deps['ImageFont'].truetype("arial.ttf", 24)
            except:
                font = self.deps['ImageFont'].load_default()
            
            # 添加半透明背景
            from PIL import Image as PILImage
            overlay = PILImage.new('RGBA', (200, 40), (0, 0, 0, 128))
            img_resized.paste(overlay, (10, 10), overlay)
            
            draw.text((15, 20), f"第 {page_num} 页", fill=(255, 255, 255), font=font)
            
            # 保存图片
            img_path = self.temp_dir / f"page_{page_num}.png"
            img_resized.save(img_path)
            
            # 处理音频
            duration = 5
            audio_path = None
            
            if page_data['use_audio'] and self.voice_engine:
                audio_path = self.temp_dir / f"audio_{page_num}.wav"
                script_text = page_data['script_text']
                
                if self.generate_speech(script_text, str(audio_path)):
                    try:
                        audio_clip = self.deps['AudioFileClip'](str(audio_path))
                        duration = max(audio_clip.duration, 3)
                        audio_clip.close()
                    except:
                        pass
                else:
                    audio_path = None
            
            # 创建视频片段
            try:
                video_clip = self.deps['ImageClip'](str(img_path), duration=duration)
                
                if audio_path and Path(audio_path).exists():
                    audio_clip = self.deps['AudioFileClip'](str(audio_path))
                    video_clip = video_clip.set_audio(audio_clip)
                
                clips.append(video_clip)
                print(f"    ✅ 第 {page_num} 页完成 (时长: {duration:.1f}秒)")
            except Exception as e:
                print(f"    ❌ 第 {page_num} 页失败: {e}")
                continue
        
        if not clips:
            print("❌ 没有成功创建任何视频片段")
            return False
        
        try:
            # 合并视频
            print("正在合并视频...")
            final_video = self.deps['concatenate_videoclips'](clips)
            
            print("正在输出视频文件...")
            final_video.write_videofile(
                str(output_path),
                fps=24,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            # 清理
            final_video.close()
            for clip in clips:
                clip.close()
            
            return True
        except Exception as e:
            print(f"❌ 视频生成失败: {e}")
            return False
    
    def convert(self, pdf_path):
        """转换PDF为视频"""
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            print(f"❌ PDF文件不存在: {pdf_path}")
            return False
        
        pdf_name = pdf_path.stem
        print(f"🎯 开始处理: {pdf_path}")
        print("="*60)
        
        try:
            # 1. 提取PDF内容
            pages_data = self.extract_pdf_content(pdf_path)
            if not pages_data:
                return False
            
            # 2. 生成初始文案
            pages_data = self.generate_initial_scripts(pages_data, pdf_name)
            
            # 3. 预览和编辑文案
            pages_data = self.preview_and_edit_scripts(pages_data)
            
            # 4. 设置语音引擎
            self.setup_voice_engine()
            
            # 5. 最终确认
            print(f"\n📋 最终配置:")
            audio_pages = sum(1 for p in pages_data if p['use_audio'])
            print(f"总页数: {len(pages_data)}")
            print(f"语音页数: {audio_pages}")
            print(f"语音引擎: {self.voice_engine or '无'}")
            
            confirm = input("\n确认开始生成视频？(y/n): ").lower()
            if confirm != 'y':
                print("❌ 用户取消操作")
                return False
            
            # 6. 创建视频
            output_name = f"{pdf_name}_perfect.mp4"
            output_path = self.output_dir / output_name
            
            success = self.create_video(pages_data, output_path)
            
            if success:
                print(f"\n🎉 转换成功!")
                print(f"输出文件: {output_path}")
                print(f"文案文件: {self.scripts_file}")
                if output_path.exists():
                    size_mb = output_path.stat().st_size / 1024 / 1024
                    print(f"文件大小: {size_mb:.1f} MB")
                return True
            else:
                print("\n❌ 转换失败")
                return False
                
        except Exception as e:
            print(f"❌ 转换过程出错: {e}")
            return False
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
        except:
            pass

def main():
    print("PDF转视频工具 - 完美版")
    print("=" * 30)
    print("✨ 支持文案生成、预览、编辑和本地文案")
    print("=" * 30)
    
    # 检查依赖
    deps = check_dependencies()
    if not deps:
        return
    
    # 获取PDF文件
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_files = list(Path(".").glob("*.pdf"))
        if not pdf_files:
            print("❌ 当前目录下没有找到PDF文件")
            return
        elif len(pdf_files) == 1:
            pdf_path = pdf_files[0]
        else:
            print("找到多个PDF文件:")
            for i, pdf_file in enumerate(pdf_files, 1):
                print(f"{i}. {pdf_file}")
            
            try:
                choice = int(input("请选择: ")) - 1
                pdf_path = pdf_files[choice]
            except:
                print("❌ 选择错误")
                return
    
    # 开始转换
    converter = PerfectPDFConverter(deps)
    converter.convert(pdf_path)

if __name__ == "__main__":
    main()
