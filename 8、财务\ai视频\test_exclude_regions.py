#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试区域排除功能
"""

import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_image_with_ads():
    """创建包含广告和无关内容的测试图片"""
    width = 800
    height = 1200
    
    # 创建图片
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体
    try:
        title_font = ImageFont.truetype("arial.ttf", 28)
        content_font = ImageFont.truetype("arial.ttf", 18)
        small_font = ImageFont.truetype("arial.ttf", 14)
    except:
        title_font = ImageFont.load_default()
        content_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 绘制主要内容区域
    content_sections = [
        (50, 50, 750, 200, 'lightblue', '第一章：重要内容'),
        (50, 250, 750, 400, 'lightgreen', '第二章：核心知识'),
        (50, 450, 750, 600, 'lightyellow', '第三章：关键信息'),
        (50, 650, 750, 800, 'lightpink', '第四章：总结要点'),
        (50, 850, 750, 1000, 'lightcyan', '第五章：结论部分')
    ]
    
    for x1, y1, x2, y2, color, title in content_sections:
        # 绘制内容区域
        draw.rectangle([x1, y1, x2, y2], fill=color, outline='black', width=2)
        
        # 添加标题
        draw.text((x1 + 20, y1 + 20), title, fill='black', font=title_font)
        
        # 添加内容
        for i in range(4):
            content_y = y1 + 60 + i * 25
            if content_y < y2 - 20:
                draw.text((x1 + 40, content_y), f"• 这是{title}的重要内容第{i+1}行", 
                         fill='darkblue', font=content_font)
    
    # 绘制需要排除的区域（广告、水印等）
    exclude_areas = [
        # 顶部广告横幅
        (0, 0, 800, 40, 'red', '🚫 顶部广告横幅 - 需要删除'),
        
        # 左侧广告栏
        (0, 200, 80, 450, 'orange', '🚫\n左侧\n广告\n需要\n删除'),
        
        # 右侧水印
        (720, 300, 800, 500, 'purple', '🚫\n水印\n区域\n删除'),
        
        # 中间弹窗广告
        (300, 420, 500, 480, 'darkred', '🚫 弹窗广告 - 删除'),
        
        # 底部版权信息
        (0, 1000, 800, 1050, 'brown', '🚫 底部版权信息栏 - 需要删除'),
        
        # 页面中的小广告
        (600, 150, 720, 200, 'magenta', '🚫 小广告'),
        (80, 750, 200, 800, 'darkgreen', '🚫 侧边栏')
    ]
    
    for x1, y1, x2, y2, color, label in exclude_areas:
        # 绘制需要排除的区域
        draw.rectangle([x1, y1, x2, y2], fill=color, outline='darkred', width=3)
        
        # 添加标签
        lines = label.split('\n')
        for i, line in enumerate(lines):
            text_y = y1 + 5 + i * 15
            if text_y < y2 - 10:
                draw.text((x1 + 5, text_y), line, fill='white', font=small_font)
    
    # 添加坐标网格（帮助定位）
    for x in range(0, width, 100):
        draw.line([x, 0, x, height], fill='lightgray', width=1)
        if x > 0:
            draw.text((x - 15, 5), str(x), fill='gray', font=small_font)
    
    for y in range(0, height, 100):
        draw.line([0, y, width, y], fill='lightgray', width=1)
        if y > 0:
            draw.text((5, y - 15), str(y), fill='gray', font=small_font)
    
    # 添加说明文字
    draw.text((10, height - 30), "红色/橙色/紫色区域为需要排除的广告和无关内容", 
             fill='black', font=content_font)
    
    return img

def test_exclude_functionality():
    """测试排除区域功能"""
    print("创建包含广告的测试图片...")
    test_img = create_test_image_with_ads()
    
    print(f"测试图片尺寸: {test_img.width} x {test_img.height}")
    
    try:
        from gui_video_converter import ManualSplitDialog
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("打开手动分割对话框...")
        print("\n🎯 排除区域功能测试说明:")
        print("1. 首先添加分割点（在Y=200, 400, 600, 800位置点击）")
        print("2. 点击'排除区域模式'按钮")
        print("3. 拖拽框选要完全删除的区域：")
        print("   • 顶部广告横幅 (0, 0) -> (800, 40)")
        print("   • 左侧广告栏 (0, 200) -> (80, 450)")
        print("   • 右侧水印 (720, 300) -> (800, 500)")
        print("   • 弹窗广告 (300, 420) -> (500, 480)")
        print("   • 底部版权 (0, 1000) -> (800, 1050)")
        print("4. 双击排除区域可以删除")
        print("5. 点击'退出排除模式'返回分割模式")
        print("6. 完成后点击'完成分割'")
        print("\n✨ 排除的区域将完全不出现在最终视频中！")
        
        # 创建手动分割对话框
        dialog = ManualSplitDialog(root, test_img, "排除区域测试")
        
        # 等待对话框关闭
        root.wait_window(dialog.dialog)
        
        # 检查结果
        if hasattr(dialog, 'split_result') and dialog.split_result:
            print(f"\n✅ 分割和排除成功！")
            print(f"生成片段数: {len(dialog.split_result)}")
            print(f"使用的排除区域数: {len(dialog.exclude_regions)}")
            
            # 保存结果图片以便查看
            for i, segment in enumerate(dialog.split_result):
                img = segment['image']
                output_path = f"exclude_test_result_segment_{i+1}.png"
                img.save(output_path)
                print(f"  片段 {i+1}: {img.width}x{img.height}px -> {output_path}")
            
            if dialog.exclude_regions:
                print(f"\n排除区域:")
                for i, (x1, y1, x2, y2) in enumerate(dialog.exclude_regions):
                    print(f"  区域 {i+1}: ({x1}, {y1}) -> ({x2}, {y2}) [已完全删除]")
            
            print(f"\n🎉 成功！排除的区域已完全从视频中删除！")
        else:
            print("❌ 分割被取消或失败")
        
        root.destroy()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保gui_video_converter.py在同一目录下")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("区域排除功能测试")
    print("=" * 30)
    
    print("功能说明:")
    print("🚫 排除区域模式:")
    print("  - 拖拽框选：选择要完全删除的区域")
    print("  - 单击排除框：选中区域")
    print("  - 双击排除框：删除区域")
    print("  - 支持多个排除区域")
    print("  - 排除的区域将完全不出现在最终视频中")
    print()
    print("📐 分割模式:")
    print("  - 单击空白处：添加分割点")
    print("  - 拖拽分割线：移动位置")
    print("  - 双击分割线：删除")
    print()
    
    choice = input("是否开始测试？(y/n): ").lower()
    if choice == 'y':
        test_exclude_functionality()
    else:
        print("测试取消")

if __name__ == "__main__":
    main()
